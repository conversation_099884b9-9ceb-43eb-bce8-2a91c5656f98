# Progress Bar Usage Guide

> **Document Purpose**: This document provides comprehensive guidance on using the centralized progress indicator system in ProManage forms. The system ensures consistent user feedback during long-running operations while keeping form code minimal.

## 1. Overview

The ProManage application uses a centralized progress indicator system built around the `ProgressIndicatorService` singleton class. This service controls the progress bar in the MainFrame status panel, providing visual feedback to users **specifically during database operations** when the application is communicating with the database.

### 1.1 Key Features

- **Centralized Control**: Single service manages the MainFrame progress bar
- **Minimal Form Code**: Simple two-line implementation in forms
- **Thread Safety**: Safe for use in multi-threaded operations
- **Nested Operations**: Supports multiple concurrent operations through reference counting
- **Minimum Display Time**: Ensures progress bar is visible for at least 500ms for better UX
- **Automatic Cleanup**: Handles errors and ensures proper cleanup

### 1.2 Architecture

```
Database Operation → ProgressIndicatorService → MainFrame StatusProgressBar
```

The service acts as a bridge between database operations and the MainFrame's progress bar control, ensuring consistent behavior across all forms during database communication.

## 2. Basic Usage Patterns

### 2.1 Standard Pattern (Recommended)

This is the standard pattern for **all database operations** and should be used consistently:

```csharp
using ProManage.Modules.UI;

// In your form method
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Your database operation here
    var result = await DatabaseOperation();

    // Process database results
    ProcessResults(result);
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

### 2.2 Synchronous Operations

For synchronous database operations:

```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Synchronous database operation
    DataTable result = EstimateFormRepository.GetAllEstimates();

    // Update UI with results
    gridControl.DataSource = result;
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

### 2.3 Multiple Sequential Operations

For multiple operations that should show continuous progress:

```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // First operation
    var estimates = EstimateFormRepository.GetAllEstimates();

    // Second operation (progress bar stays visible)
    var customers = CustomerRepository.GetAllCustomers();

    // Third operation
    var products = ProductRepository.GetAllProducts();

    // Process all data
    ProcessData(estimates, customers, products);
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

## 3. Advanced Usage Scenarios

### 3.1 Nested Operations

The service automatically handles nested operations through reference counting:

```csharp
// Outer operation
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // This will increment the operation count
    ProcessEstimates();

    // This will also increment the operation count
    ProcessCustomers();
}
finally
{
    ProgressIndicatorService.Instance.HideProgress(); // Decrements count
}

private void ProcessEstimates()
{
    ProgressIndicatorService.Instance.ShowProgress(); // Increments count
    try
    {
        // Database operation
        var estimates = EstimateFormRepository.GetAllEstimates();
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress(); // Decrements count
    }
}
```

### 3.2 Error Recovery

For critical operations where you need to ensure progress bar cleanup:

```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Critical operation that might fail
    var result = CriticalDatabaseOperation();
}
catch (Exception ex)
{
    // Log error
    Debug.WriteLine($"Critical operation failed: {ex.Message}");

    // Reset progress indicator if needed
    if (ProgressIndicatorService.Instance.IsVisible)
    {
        ProgressIndicatorService.Instance.Reset();
    }

    // Show user-friendly error message
    MessageBox.Show("Operation failed. Please try again.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

### 3.3 Debugging with Messages

For debugging purposes, you can include optional messages:

```csharp
ProgressIndicatorService.Instance.ShowProgress("Loading estimate data from database");
try
{
    // Database operation
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

## 4. Integration Examples

### 4.1 Form Load Operations

```csharp
private async void EstimateForm_Load(object sender, EventArgs e)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        // Load initial data
        await LoadEstimateData();

        // Setup UI
        InitializeGridColumns();

        // Load dropdown data
        await LoadDropdownData();
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

### 4.2 Save Operations

```csharp
private void btnSave_Click(object sender, EventArgs e)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        // Validate data
        if (!ValidateForm())
        {
            return;
        }

        // Save to database
        bool success = EstimateFormRepository.SaveEstimate(currentEstimate);

        if (success)
        {
            MessageBox.Show("Estimate saved successfully.", "Success",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Refresh data
            LoadEstimateData();
        }
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

### 4.3 Search Operations

```csharp
private void btnSearch_Click(object sender, EventArgs e)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        string searchTerm = txtSearch.Text.Trim();

        if (string.IsNullOrEmpty(searchTerm))
        {
            // Load all records
            var allEstimates = EstimateFormRepository.GetAllEstimates();
            gridControl.DataSource = allEstimates;
        }
        else
        {
            // Search with criteria
            var searchResults = EstimateFormRepository.SearchEstimates(searchTerm);
            gridControl.DataSource = searchResults;
        }

        // Update status
        lblRecordCount.Text = $"Records found: {gridView.RowCount}";
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

## 5. Best Practices

### 5.1 When to Use Progress Indicators

**Always Use For (Database Operations Only):**
- Database CRUD operations (Create, Read, Update, Delete)
- Database search and query operations
- Database connection and initialization
- Report generation from database
- Form initialization that loads data from database
- Navigation operations that query database

**Don't Use For:**
- Simple UI updates
- Client-side validation operations
- Quick calculations
- File I/O operations
- Network requests (non-database)
- Navigation between records (if data already loaded)
- Any operation that doesn't communicate with the database

### 5.2 Code Organization

**Keep It Simple:**
```csharp
// Good - Simple and clear
ProgressIndicatorService.Instance.ShowProgress();
try
{
    DoWork();
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

**Avoid Complex Nesting:**
```csharp
// Avoid - Too complex
if (condition)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        if (anotherCondition)
        {
            // Complex nested logic
        }
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

### 5.3 Error Handling

**Always Use Finally Blocks:**
- Ensures progress bar is hidden even if exceptions occur
- Prevents progress bar from staying visible indefinitely
- Maintains consistent user experience

**Consider Reset for Critical Errors:**
- Use `Reset()` method only when operation counting might be corrupted
- Typically needed only in exceptional error scenarios
- Always log when using Reset for debugging purposes

## 6. Common Pitfalls and Solutions

### 6.1 Progress Bar Stuck Visible

**Problem:** Progress bar remains visible after operation completes.

**Causes:**
- Missing `finally` block
- Exception thrown before `HideProgress()` call
- Unbalanced `ShowProgress()`/`HideProgress()` calls

**Solution:**
```csharp
// Always use try-finally
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Operation
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}

// For emergency cleanup
ProgressIndicatorService.Instance.Reset();
```

### 6.2 Progress Bar Not Showing

**Problem:** Progress bar doesn't appear during operations.

**Causes:**
- Service not initialized in MainFrame
- Operation completes too quickly (less than 500ms)
- UI thread blocking

**Solution:**
- Verify MainFrame initialization
- Use longer test operations to verify functionality
- Ensure `Application.DoEvents()` is called appropriately

### 6.3 Thread Safety Issues

**Problem:** Cross-thread operation exceptions.

**Solution:**
The service handles thread safety automatically through `InvokeRequired` checks. No additional code needed in forms.

## 7. Debugging and Troubleshooting

### 7.1 Debug Output

The service provides detailed debug output. Monitor the Debug console for:
- Initialization status
- Show/hide operations
- Operation count tracking
- Error messages

### 7.2 Service Status Checking

```csharp
// Check if service is properly initialized
if (!ProgressIndicatorService.Instance.IsInitialized)
{
    Debug.WriteLine("ProgressIndicatorService not initialized!");
}

// Check current visibility status
if (ProgressIndicatorService.Instance.IsVisible)
{
    Debug.WriteLine("Progress indicator is currently visible");
}
```

### 7.3 Common Debug Scenarios

**Service Not Initialized:**
```
Warning: ProgressIndicatorService not initialized. Call Initialize() first.
```
**Solution:** Verify MainFrame initialization code is uncommented and running.

**Operation Count Mismatch:**
```
Progress indicator still needed. Remaining operations: 2
```
**Solution:** Check for unbalanced ShowProgress/HideProgress calls.

## 8. Performance Considerations

### 8.1 Minimum Display Time

The service enforces a minimum 500ms display time to ensure users can see the progress indicator. For very fast operations, this may add a slight delay, but improves user experience by providing consistent visual feedback.

### 8.2 UI Thread Impact

The service uses `Application.DoEvents()` to ensure immediate UI updates. This is generally safe for the progress indicator use case but should not be overused in other contexts.

### 8.3 Memory Usage

The singleton pattern ensures minimal memory overhead. The service maintains only essential state information and references.

## 9. Future Enhancements

### 9.1 Planned Features

- **Progress Percentage**: Support for determinate progress indicators
- **Custom Messages**: Display operation-specific messages to users
- **Cancellation Support**: Allow users to cancel long-running operations
- **Multiple Progress Bars**: Support for different types of progress indicators

### 9.2 Extensibility

The current architecture supports future enhancements while maintaining backward compatibility with existing form implementations.

---

> **📖 Related Documentation**:
> - [ProjectStructure.md](ProjectStructure.md) - Overall project organization
> - [ProManage-Documentation.md](ProManage-Documentation.md) - Complete system overview
> - [DatabaseArchitecture.md](DatabaseArchitecture.md) - Database operation patterns
