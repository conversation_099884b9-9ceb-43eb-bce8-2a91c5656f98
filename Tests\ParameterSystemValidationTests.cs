// Parameter System Validation Tests - Validates the unified parameter system implementation
// Usage: Run this to verify that the parameter migration was successful and everything works

using System;
using System.Diagnostics;
using ProManage.Modules.Services;

namespace ProManage.Tests
{
    /// <summary>
    /// Validates that the unified parameter implementation is working correctly
    /// and that the migration from old services was successful
    /// </summary>
    public static class ParameterSystemValidationTests
    {
        /// <summary>
        /// Runs a comprehensive validation of the parameter implementation
        /// </summary>
        public static bool ValidateImplementation()
        {
            Debug.WriteLine("=== Parameter Implementation Validation Starting ===");

            try
            {
                // Test 1: Verify UnifiedParameterManager is accessible
                if (!TestUnifiedParameterManagerAccess())
                {
                    Debug.WriteLine("✗ FAILED: UnifiedParameterManager access test");
                    return false;
                }

                // Test 2: Verify initialization works
                if (!TestInitialization())
                {
                    Debug.WriteLine("✗ FAILED: Initialization test");
                    return false;
                }

                // Test 3: Verify basic parameter access
                if (!TestBasicParameterAccess())
                {
                    Debug.WriteLine("✗ FAILED: Basic parameter access test");
                    return false;
                }

                // Test 4: Verify typed parameter access
                if (!TestTypedParameterAccess())
                {
                    Debug.WriteLine("✗ FAILED: Typed parameter access test");
                    return false;
                }

                // Test 5: Verify category access
                if (!TestCategoryAccess())
                {
                    Debug.WriteLine("✗ FAILED: Category access test");
                    return false;
                }

                // Test 6: Verify migration compatibility
                if (!TestMigrationCompatibility())
                {
                    Debug.WriteLine("✗ FAILED: Migration compatibility test");
                    return false;
                }

                Debug.WriteLine("✓ ALL TESTS PASSED: Parameter implementation is working correctly");
                Debug.WriteLine("=== Parameter Implementation Validation Completed Successfully ===");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ VALIDATION FAILED: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// Tests that UnifiedParameterManager is accessible and singleton works
        /// </summary>
        private static bool TestUnifiedParameterManagerAccess()
        {
            Debug.WriteLine("--- Testing UnifiedParameterManager Access ---");

            try
            {
                // Test singleton access
                var instance1 = UnifiedParameterManager.Instance;
                var instance2 = UnifiedParameterManager.Instance;

                if (instance1 == null)
                {
                    Debug.WriteLine("✗ UnifiedParameterManager.Instance returned null");
                    return false;
                }

                if (instance1 != instance2)
                {
                    Debug.WriteLine("✗ Singleton pattern failed - different instances returned");
                    return false;
                }

                Debug.WriteLine("✓ UnifiedParameterManager singleton access working");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Error accessing UnifiedParameterManager: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests parameter manager initialization
        /// </summary>
        private static bool TestInitialization()
        {
            Debug.WriteLine("--- Testing Initialization ---");

            try
            {
                bool initialized = UnifiedParameterManager.Instance.Initialize();
                Debug.WriteLine($"Initialization result: {initialized}");

                // Check if parameters were loaded
                int parameterCount = UnifiedParameterManager.Instance.ParameterCount;
                Debug.WriteLine($"Parameter count: {parameterCount}");

                if (parameterCount == 0)
                {
                    Debug.WriteLine("⚠ Warning: No parameters loaded - this might be expected if database is empty");
                }

                Debug.WriteLine("✓ Initialization test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Initialization failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests basic string parameter access
        /// </summary>
        private static bool TestBasicParameterAccess()
        {
            Debug.WriteLine("--- Testing Basic Parameter Access ---");

            try
            {
                // Test string parameter with default
                string currency = UnifiedParameterManager.Instance.GetString("CURRENCY", "USD");
                Debug.WriteLine($"Currency parameter: {currency}");

                // Test parameter existence check
                bool hasCurrency = UnifiedParameterManager.Instance.HasParameter("CURRENCY");
                Debug.WriteLine($"Has CURRENCY parameter: {hasCurrency}");

                // Test non-existent parameter returns default
                string nonExistent = UnifiedParameterManager.Instance.GetString("NON_EXISTENT_PARAM", "DEFAULT_VALUE");
                if (nonExistent != "DEFAULT_VALUE")
                {
                    Debug.WriteLine($"✗ Non-existent parameter should return default 'DEFAULT_VALUE', got '{nonExistent}'");
                    return false;
                }

                Debug.WriteLine("✓ Basic parameter access test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Basic parameter access failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests typed parameter access methods
        /// </summary>
        private static bool TestTypedParameterAccess()
        {
            Debug.WriteLine("--- Testing Typed Parameter Access ---");

            try
            {
                // Test integer access
                int decimals = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
                Debug.WriteLine($"Decimals (int): {decimals}");

                // Test boolean access
                bool showGST = UnifiedParameterManager.Instance.GetBool("SHOW_GST", false);
                Debug.WriteLine($"Show GST (bool): {showGST}");

                // Test decimal access
                decimal taxRate = UnifiedParameterManager.Instance.GetDecimal("TAX_RATE", 0.05m);
                Debug.WriteLine($"Tax Rate (decimal): {taxRate}");

                // Test DateTime access
                DateTime testDate = UnifiedParameterManager.Instance.GetDateTime("TEST_DATE", DateTime.Now);
                Debug.WriteLine($"Test Date (DateTime): {testDate}");

                // Test generic parameter access
                string genericCurrency = UnifiedParameterManager.Instance.GetParameter<string>("CURRENCY", "USD");
                Debug.WriteLine($"Generic currency access: {genericCurrency}");

                Debug.WriteLine("✓ Typed parameter access test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Typed parameter access failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests category-based parameter access
        /// </summary>
        private static bool TestCategoryAccess()
        {
            Debug.WriteLine("--- Testing Category Access ---");

            try
            {
                // Test Currency category
                var currency = UnifiedParameterManager.Instance.Currency;
                if (currency == null)
                {
                    Debug.WriteLine("✗ Currency category is null");
                    return false;
                }

                string symbol = currency.Symbol;
                int decimalPlaces = currency.DecimalPlaces;
                string format = currency.Format;
                string decimalFormat = currency.DecimalFormat;

                Debug.WriteLine($"Currency Symbol: {symbol}");
                Debug.WriteLine($"Currency Decimal Places: {decimalPlaces}");
                Debug.WriteLine($"Currency Format: {format}");
                Debug.WriteLine($"Currency Decimal Format: {decimalFormat}");

                // Test Company category
                var company = UnifiedParameterManager.Instance.Company;
                if (company == null)
                {
                    Debug.WriteLine("✗ Company category is null");
                    return false;
                }

                Debug.WriteLine($"Company Name: {company.Name}");
                Debug.WriteLine($"Company Address: {company.Address}");

                // Test UI category
                var ui = UnifiedParameterManager.Instance.UI;
                if (ui == null)
                {
                    Debug.WriteLine("✗ UI category is null");
                    return false;
                }

                Debug.WriteLine($"Show Tooltips: {ui.ShowTooltips}");
                Debug.WriteLine($"Default Page Size: {ui.DefaultPageSize}");

                // Test Business category
                var business = UnifiedParameterManager.Instance.Business;
                if (business == null)
                {
                    Debug.WriteLine("✗ Business category is null");
                    return false;
                }

                Debug.WriteLine($"Tax Rate: {business.TaxRate}");
                Debug.WriteLine($"Show GST: {business.ShowGST}");

                Debug.WriteLine("✓ Category access test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Category access failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests that the migration from old services was successful
        /// </summary>
        private static bool TestMigrationCompatibility()
        {
            Debug.WriteLine("--- Testing Migration Compatibility ---");

            try
            {
                // Verify that UnifiedParameterManager provides the same functionality as old services

                // Test currency functionality (replaces CurrencyFormattingService)
                string currencySymbol = UnifiedParameterManager.Instance.Currency.Symbol;
                int decimalPlaces = UnifiedParameterManager.Instance.Currency.DecimalPlaces;
                string currencyFormat = UnifiedParameterManager.Instance.Currency.Format;

                if (string.IsNullOrEmpty(currencySymbol))
                {
                    Debug.WriteLine("⚠ Warning: Currency symbol is empty");
                }

                if (decimalPlaces < 0 || decimalPlaces > 10)
                {
                    Debug.WriteLine("⚠ Warning: Decimal places seems unusual");
                }

                if (string.IsNullOrEmpty(currencyFormat))
                {
                    Debug.WriteLine("⚠ Warning: Currency format is empty");
                }

                Debug.WriteLine($"✓ Currency functionality available (Symbol: {currencySymbol}, Decimals: {decimalPlaces})");

                // Test company functionality (replaces TypedParameterManager)
                string companyName = UnifiedParameterManager.Instance.Company.Name;
                Debug.WriteLine($"✓ Company functionality available (Name: {companyName})");

                // Test typed access functionality (replaces ParameterCacheServiceExtensions)
                int intValue = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
                bool boolValue = UnifiedParameterManager.Instance.GetBool("SHOW_GST", false);
                decimal decimalValue = UnifiedParameterManager.Instance.GetDecimal("TAX_RATE", 0.05m);

                Debug.WriteLine($"✓ Typed access functionality available (Int: {intValue}, Bool: {boolValue}, Decimal: {decimalValue})");

                Debug.WriteLine("✓ Migration compatibility test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Migration compatibility test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Quick validation method for use in application startup
        /// </summary>
        public static bool QuickValidation()
        {
            try
            {
                // Quick test - just verify basic functionality
                var instance = UnifiedParameterManager.Instance;
                bool initialized = instance.Initialize();
                string currency = instance.GetString("CURRENCY", "USD");
                string format = instance.Currency.Format;

                Debug.WriteLine($"Quick validation passed - Currency: {currency}, Format: {format}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Quick validation failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Runs all parameter validation tests manually with console and debug output
        /// Call this from any form or method for immediate testing
        /// </summary>
        public static void RunManualTests()
        {
            Console.WriteLine("=== Manual Parameter Validation Test Runner ===");
            Debug.WriteLine("=== Manual Parameter Validation Test Runner ===");

            try
            {
                // Test 1: Quick Validation
                Console.WriteLine("\n--- Running Quick Validation ---");
                Debug.WriteLine("--- Running Quick Validation ---");

                bool quickResult = QuickValidation();
                string quickStatus = quickResult ? "✓ PASSED" : "✗ FAILED";

                Console.WriteLine($"Quick Validation: {quickStatus}");
                Debug.WriteLine($"Quick Validation: {quickStatus}");

                // Test 2: Comprehensive Validation
                Console.WriteLine("\n--- Running Comprehensive Validation ---");
                Debug.WriteLine("--- Running Comprehensive Validation ---");

                bool comprehensiveResult = ValidateImplementation();
                string comprehensiveStatus = comprehensiveResult ? "✓ PASSED" : "✗ FAILED";

                Console.WriteLine($"Comprehensive Validation: {comprehensiveStatus}");
                Debug.WriteLine($"Comprehensive Validation: {comprehensiveStatus}");

                // Summary
                Console.WriteLine("\n=== Test Summary ===");
                Debug.WriteLine("=== Test Summary ===");

                if (quickResult && comprehensiveResult)
                {
                    Console.WriteLine("✓ ALL TESTS PASSED - Parameter system is working correctly");
                    Debug.WriteLine("✓ ALL TESTS PASSED - Parameter system is working correctly");
                }
                else
                {
                    Console.WriteLine("✗ SOME TESTS FAILED - Check debug output for details");
                    Debug.WriteLine("✗ SOME TESTS FAILED - Check debug output for details");
                }
            }
            catch (Exception ex)
            {
                string errorMsg = $"✗ TEST RUNNER ERROR: {ex.Message}";
                Console.WriteLine(errorMsg);
                Debug.WriteLine(errorMsg);
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\n=== Manual Test Runner Completed ===");
            Debug.WriteLine("=== Manual Test Runner Completed ===");
        }

        /// <summary>
        /// Simplified test method for quick checks
        /// </summary>
        public static bool RunQuickTest()
        {
            try
            {
                Debug.WriteLine("Running quick parameter validation test...");
                bool result = QuickValidation();
                Debug.WriteLine($"Quick test result: {(result ? "PASSED" : "FAILED")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Quick test error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Example method showing how to call the validator from any part of your application
        /// </summary>
        public static void ExampleUsage()
        {
            // Method 1: Quick validation (recommended for regular checks)
            Debug.WriteLine("=== Running Quick Validation ===");
            bool quickResult = QuickValidation();
            Debug.WriteLine($"Quick validation result: {(quickResult ? "PASSED" : "FAILED")}");

            // Method 2: Comprehensive validation (recommended for thorough testing)
            Debug.WriteLine("\n=== Running Comprehensive Validation ===");
            bool comprehensiveResult = ValidateImplementation();
            Debug.WriteLine($"Comprehensive validation result: {(comprehensiveResult ? "PASSED" : "FAILED")}");

            // Method 3: Use the manual test runner (includes both tests)
            Debug.WriteLine("\n=== Running Manual Test Runner ===");
            RunManualTests();

            // Method 4: Quick test only (for minimal validation)
            Debug.WriteLine("\n=== Running Quick Test Only ===");
            RunQuickTest();
        }
    }
}
