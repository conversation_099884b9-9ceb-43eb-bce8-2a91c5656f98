# ProManage Project Structure

This document provides a comprehensive overview of the ProManage project's folder hierarchy, organization principles, and file placement guidelines.

## Root Directory Structure

```
ProManage/
├── Forms/                          # Windows Forms UI components
├── Modules/                        # Core business logic and data access
├── Reports/                        # DevExpress report templates
├── Resources/                      # Application resources (images, icons)
├── ProManage.Services/            # Service layer project
├── docs/                          # Project documentation
├── Waste/                         # Legacy/backup files (VB.NET conversion artifacts)
├── References/                    # External library references
├── packages/                      # NuGet package cache
├── bin/                          # Build output directory
├── obj/                          # Build intermediate files
├── ProManage.csproj              # Main project file
├── App.config                    # Application configuration
├── Development.config            # Development environment configuration
└── Program.cs                    # Application entry point
```

## Modules Folder Organization

The `Modules/` folder contains the core business logic organized by functional area:

### Modules/Connections/
**Purpose**: Centralized database connectivity and transaction management
- `DatabaseConnectionManager.cs` - Singleton connection manager
- `DatabaseTransactionService.cs` - Transaction handling service
- `DatabaseQueryExecutor.cs` - Query execution utilities

### Modules/Data/
**Purpose**: Data access layer with form-specific repositories
- `EstimateForm-Repository.cs` - Estimate data operations
- `EstimateForm-QueryService.cs` - Estimate query execution
- `LoginForm-UserManager.cs` - User authentication and session management
- `SQLQueries.cs` - SQL query constants and organization

### Modules/Models/
**Purpose**: Data models and entity classes
- `EstimateForm-HeaderModel.cs` - Estimate header data model
- `EstimateForm-DetailModel.cs` - Estimate detail line items model
- `LoginForm-UserModel.cs` - User entity model

### Modules/Helpers/
**Purpose**: Utility classes and form-specific helper functions
- `ConfigurationHelper.cs` - Application configuration management
- `SQLQueryLoader.cs` - SQL file loading and caching utilities
- `EstimateForm-Helper.cs` - UI helpers for EstimateForm (grid, navigation, form state)
- `EstimateForm-Validation.cs` - Data validation and business rules for EstimateForm
- `[FormName]-Helper.cs` - Form-specific UI helpers and utilities
- `[FormName]-Validation.cs` - Form-specific validation logic

### Modules/UI/
**Purpose**: UI-specific services and utilities
- `ProgressIndicatorService.cs` - Centralized progress indicator service for MainFrame status bar

### Modules/Licensing/
**Purpose**: License management and validation
- `LicenseManager.cs` - License validation and management
- `LicenseUsageMode.cs` - License usage mode enumeration

### Modules/Reports/
**Purpose**: Report generation and management
- `ReportManager.cs` - Report creation and display utilities

### Modules/Procedures/
**Purpose**: SQL query files organized by functional module

```
Modules/Procedures/
├── Estimate/                      # Estimate-related SQL queries
│   ├── EstimateCRUD.sql          # Create, Read, Update, Delete operations
│   ├── EstimateDelete.sql        # Delete operations
│   ├── EstimateListing.sql       # List and search operations
│   ├── EstimateNavigation.sql    # Navigation queries (first, last, next, prev)
│   ├── EstimateRetrieval.sql     # Retrieval operations
│   └── EstimateUtilities.sql     # Utility queries
├── SQLQuery/                      # SQL Query tool operations
│   ├── GetAllTables.sql          # Database schema queries
│   ├── GetTableColumns.sql
│   ├── GetTableConstraints.sql
│   ├── GetTableData.sql
│   ├── GetTableForeignKeys.sql
│   ├── GetTableIndexes.sql
│   └── GetTablePrimaryKey.sql
├── System/                        # System-level queries
│   ├── GetTableColumns.sql
│   └── GetTables.sql
├── User/                          # User management queries
│   ├── GetUser.sql
│   ├── SaveUser.sql
│   └── ValidateUser.sql
└── README.md                      # SQL procedures documentation
```

## Forms Directory Structure

```
Forms/
├── DatabaseForm.cs               # Database configuration form
├── DatabaseForm.Designer.cs
├── EstimateForm.cs              # Main estimate management form
├── EstimateForm.Designer.cs
├── LoginForm.cs                 # User authentication form
├── LoginForm.Designer.cs
├── MainFrame.cs                 # Main application window
├── MainFrame.Designer.cs
├── SQLQueryForm.cs              # SQL query execution tool
├── SQLQueryForm.Designer.cs
├── TestForm.cs                  # Development testing form
└── TestForm.Designer.cs
```

## Reports Directory Structure

```
Reports/
├── EstimateReport.cs            # Estimate report template
├── EstimateReport.Designer.cs   # Report designer file
└── EstimateReport.resx          # Report resources
```

## Dependencies and Relationships

### Data Flow Architecture
1. **Forms Layer** → **Modules/Data** → **Modules/Connections** → **Database**
2. **Forms** use **Modules/Models** for data binding
3. **Modules/Data** uses **Modules/Helpers** for SQL loading
4. **Modules/Reports** uses **Modules/Models** for report data

### Key Relationships
- **EstimateForm.cs** ↔ **EstimateForm-Repository.cs** ↔ **EstimateForm-HeaderModel.cs**
- **LoginForm.cs** ↔ **LoginForm-UserManager.cs** ↔ **LoginForm-UserModel.cs**
- **All Data classes** → **DatabaseConnectionManager.cs** for database access
- **SQL queries** loaded via **SQLQueryLoader.cs** from **Modules/Procedures/**

### Namespace Organization
- **Forms**: `ProManage.Forms`
- **Modules**: `ProManage.Modules.[SubfolderName]`
- **Reports**: `ProManage.Reports`
- **Services**: `ProManage.Services` (separate project)

## File Placement Guidelines

### When to place files in each folder:

**Modules/Data/**
- Repository classes for specific forms
- Query service classes
- Data access utilities

**Modules/Models/**
- Entity classes representing database tables
- Data transfer objects
- Form-specific model classes

**Modules/Helpers/**
- Utility classes used across multiple modules
- Configuration management
- File I/O utilities

**Modules/UI/**
- UI-specific services (progress indicators, notifications)
- Form state management utilities
- UI helper classes

**Modules/Connections/**
- Database connection management
- Transaction handling
- Query execution utilities

**Modules/Procedures/**
- All SQL query files
- Organized by functional module
- Named queries within files using comments

## File Splitting Guidelines

### When to Split Large Files

Files should be split when they exceed **1000 lines** (preferred limit: **500 lines**) to maintain readability and performance. The splitting strategy depends on the file type:

### Form File Splitting Strategy

**Forms Folder Rules:**
- Only 3 files per form: `.cs`, `.designer.cs`, and `.resx`
- All supporting logic goes to `modules/` folder
- Main form file should contain only essential UI logic

**Example: EstimateForm Splitting**
```
Forms/
├── EstimateForm.cs (≤800 lines)           # Main form with essential UI logic
├── EstimateForm.designer.cs               # Auto-generated designer file
└── EstimateForm.resx                      # Auto-generated resources

modules/helpers/
├── EstimateForm-Helper.cs (≤900 lines)    # UI helpers, grid, navigation, form state
└── EstimateForm-Validation.cs (≤600 lines) # Data validation and business rules

modules/Data/
├── EstimateForm-Repository.cs (≤900 lines) # Core CRUD + Search operations
└── EstimateForm-QueryService.cs (≤700 lines) # SQL operations and data mapping
```

### Repository File Splitting Strategy

**Large Repository Files (>1000 lines):**
- Combine CRUD operations with search functionality in main repository
- Extract complex mapping logic to separate helper methods
- Keep navigation queries in the same file for cohesion
- Split only when file exceeds 1000 lines significantly

**Example: Repository Organization**
```csharp
// EstimateForm-Repository.cs (≤900 lines)
public static class EstimateFormRepository
{
    #region Core CRUD Operations
    // GetEstimateById, SaveEstimate, DeleteEstimate

    #region Search Operations
    // GetAllEstimates, SearchEstimatesByCustomer, GetEstimateByNumber

    #region Navigation Operations
    // GetFirstEstimate, GetLastEstimate, GetNextEstimate, GetPreviousEstimate

    #region Utility Methods
    // GetNextEstimateNumber, GetEstimateDetailsById, GetEstimateCount

    #region Private Helper Methods
    // MapEstimateFromReader, InsertEstimateHeader, UpdateEstimateHeader
}
```

### Helper File Organization

**Form-Specific Helpers:**
- `[FormName]-Helper.cs` - UI operations, grid management, navigation, form state
- `[FormName]-Validation.cs` - Data validation, business rules, error handling

**Shared Helpers:**
- Place in `modules/helpers/` without form prefix
- Examples: `ConfigurationHelper.cs`, `SQLQueryLoader.cs`

### File Size Targets

| File Type | Preferred Limit | Maximum Limit | Action Required |
|-----------|----------------|---------------|-----------------|
| **Form Files** | 500 lines | 800 lines | Split to helpers |
| **Repository Files** | 500 lines | 900 lines | Combine related operations |
| **Helper Files** | 500 lines | 900 lines | Split by functionality |
| **Model Files** | 200 lines | 300 lines | Split by entity |
| **Service Files** | 500 lines | 700 lines | Split by responsibility |

## Progress Indicator System

### Overview
ProManage implements a centralized progress indicator system that provides consistent user feedback **specifically during database operations** across all forms. The system is designed to keep form code minimal while ensuring professional user experience when the application is communicating with the database.

### Architecture Components

**Core Service:**
- `Modules/UI/ProgressIndicatorService.cs` - Singleton service managing the MainFrame progress bar

**MainFrame Integration:**
- `Forms/MainFrame.cs` - Initializes the service with statusProgressBar control
- `Forms/MainFrame.Designer.cs` - Contains MarqueeProgressBarControl definition

**Key Features:**
- **Centralized Control**: Single service manages progress indication across all forms
- **Thread Safety**: Safe for use in multi-threaded operations
- **Nested Operations**: Supports multiple concurrent operations through reference counting
- **Minimum Display Time**: Ensures 500ms minimum visibility for better UX
- **Automatic Cleanup**: Handles errors and ensures proper cleanup

### Usage Pattern in Forms

**Standard Implementation (2 lines of code):**
```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Database operation (CRUD, search, etc.)
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

**When to Use (Database Operations Only):**
- Database CRUD operations (Create, Read, Update, Delete)
- Database search and query operations
- Report generation from database
- Form initialization that loads data from database
- Navigation operations that query database

**Integration Benefits:**
- **Minimal Form Code**: Only 2 lines needed per database operation
- **Consistent UX**: Same progress indication across all forms during database communication
- **Error Resilient**: Automatic cleanup even if database operations fail
- **Professional Appearance**: Smooth, responsive user feedback during database operations

> **📖 For Complete Details**: See [ProgressBarUsageGuide.md](ProgressBarUsageGuide.md) for comprehensive implementation examples, best practices, and troubleshooting guidelines.

## Best Practices for File Organization

1. **Group related functionality** in the same subfolder
2. **Use descriptive folder names** that clearly indicate purpose
3. **Maintain consistent depth** - avoid deeply nested folder structures
4. **Separate concerns** - keep UI, data, and business logic in appropriate folders
5. **Document relationships** between files and folders
6. **Follow naming conventions** consistently across all folders
7. **Prefer 1-2 files per important process** rather than many small files
8. **Keep files under 1000 lines** (500 preferred) for maintainability
9. **Use partial classes** for form splitting to maintain functionality
10. **Extract complex logic** to helper classes rather than inline code
11. **Use centralized progress indication** for all database operations

## Build Output Organization

The build process automatically copies SQL files to the output directory maintaining the same folder structure:

```
bin/Debug/
├── Modules/
│   └── Procedures/
│       ├── Estimate/
│       ├── SQLQuery/
│       ├── System/
│       └── User/
├── ProManage.exe
├── ProManage.exe.config
└── [Other assemblies and dependencies]
```

This ensures SQL queries are available at runtime for the SQLQueryLoader utility.
