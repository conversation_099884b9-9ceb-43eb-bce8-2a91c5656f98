// Main EstimateForm class - handles initialization and event wiring only
// All business logic is delegated to helper classes for better maintainability

using System;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraReports.UI;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Helpers.EstimateForm;
using ProManage.Modules.Reports;

namespace ProManage.Forms
{
    /// <summary>
    /// Main Estimate Form - handles vehicle repair estimates
    /// Uses helper classes for all business logic and operations
    /// </summary>
    public partial class EstimateForm : RibbonForm
    {
        #region Private Fields

        private EstimateFormHeaderModel currentEstimate;
        private bool isEditMode = false;
        private DataTable gridDataTable;

        #endregion

        #region Public Properties

        /// <summary>
        /// Exposes the grid data table for helper classes
        /// </summary>
        public DataTable GridDataTable => gridDataTable;

        /// <summary>
        /// Exposes the current estimate for helper classes
        /// </summary>
        public EstimateFormHeaderModel CurrentEstimate => currentEstimate;

        /// <summary>
        /// Exposes the edit mode state for helper classes
        /// </summary>
        public bool IsEditMode => isEditMode;

        #endregion

        #region Constructor and Initialization

        /// <summary>
        /// Constructor - initializes the form and sets up all components
        /// </summary>
        public EstimateForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// Initializes the form after component initialization
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                Debug.WriteLine("=== EstimateForm.InitializeForm: Starting ===");

                // Set up the grid data table and columns
                SetupGridDataTable();

                // Set up grid view columns
                SetupGridViewColumns();

                // Wire up button events
                SetupButtonEvents();

                // Set up keyboard shortcuts
                SetupKeyboardShortcuts();

                // Set up status toggle
                SetupStatusToggle();

                // Load the first estimate if available
                LoadFirstEstimate();

                Debug.WriteLine("=== EstimateForm.InitializeForm: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeForm: {ex.Message}");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Initialization Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up the grid data table with proper structure
        /// </summary>
        private void SetupGridDataTable()
        {
            try
            {
                gridDataTable = EstimateFormHelper.SetupGridDataTable(GridControl1, GridView1);
                Debug.WriteLine("Grid data table setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid data table: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up the grid view columns with proper formatting
        /// </summary>
        private void SetupGridViewColumns()
        {
            try
            {
                EstimateFormHelper.SetupGridViewColumns(GridView1);
                EstimateFormHelper.EnsureGridColumnsVisible(gridDataTable, GridView1, GridControl1);

                // Setup delete button events
                EstimateFormGridEnhancements.SetupDeleteButtonEvents(GridView1, gridDataTable);

                // Setup grid enhancements including footer totals
                EstimateFormGridEnhancements.SetupGridEnhancements(GridView1);

                // Setup uppercase conversion for Part Number and Description fields
                SetupUppercaseConversion();

                Debug.WriteLine("Grid view columns setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid view columns: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Wires up all button events to their respective handlers
        /// </summary>
        private void SetupButtonEvents()
        {
            try
            {
                // Navigation button events
                BarButtonItemFirst.ItemClick += (s, e) => NavigateToFirst();
                BarButtonItemPrevious.ItemClick += (s, e) => NavigateToPrevious();
                BarButtonItemNext.ItemClick += (s, e) => NavigateToNext();
                BarButtonItemLast.ItemClick += (s, e) => NavigateToLast();

                // CRUD operation button events
                BarButtonItemNew.ItemClick += (s, e) => CreateNewEstimate();
                BarButtonItemEdit.ItemClick += (s, e) => EditCurrentEstimate();
                BarButtonItemSave.ItemClick += (s, e) => SaveCurrentEstimate();
                BarButtonItemCancel.ItemClick += (s, e) => CancelCurrentOperation();
                BarButtonItemDelete.ItemClick += (s, e) => DeleteCurrentEstimate();

                // Grid operation button events
                BarButtonItemAddRow.ItemClick += (s, e) => AddNewGridRow();

                // Print button event
                BarButtonItemPrint.ItemClick += (s, e) => PrintCurrentEstimate();

                // Print Preview button event
                BarButtonPrintPreview.ItemClick += (s, e) => ShowPrintPreview();

                Debug.WriteLine("Button events setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up button events: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up keyboard shortcuts for navigation
        /// </summary>
        private void SetupKeyboardShortcuts()
        {
            try
            {
                KeyPreview = true;
                KeyDown += EstimateForm_KeyDown;
                Debug.WriteLine("Keyboard shortcuts setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up keyboard shortcuts: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up the status toggle button
        /// </summary>
        private void SetupStatusToggle()
        {
            try
            {
                // Wire up the toggle switch event
                tglStatus.CheckedChanged += BarToggleSwitchItem1_CheckedChanged;
                Debug.WriteLine("Status toggle setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up status toggle: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Loads the first estimate on form initialization
        /// </summary>
        private void LoadFirstEstimate()
        {
            try
            {
                var firstEstimate = EstimateFormNavigation.NavigateToFirst();
                if (firstEstimate != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(firstEstimate);
                    EstimateFormDataMapper.LoadEstimateToForm(this, firstEstimate);
                    currentEstimate = firstEstimate;
                    EstimateFormNavigation.UpdateNavigationPosition(this, currentEstimate);
                    UpdateToggleState(); // Ensure toggle state is correct
                    Debug.WriteLine($"Loaded first estimate: {firstEstimate.EstimateNo}");
                }
                else
                {
                    Debug.WriteLine("No estimates found in database");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading first estimate: {ex.Message}");
                // Continue without loading - form will be empty but functional
            }

            // Update button states after attempting to load first estimate
            // This ensures navigation buttons are enabled if there are estimates in database
            EstimateFormHelper.UpdateButtonStates(this, isEditMode);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles keyboard shortcuts for navigation
        /// </summary>
        private void EstimateForm_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // DevExpress handles backstage view closing automatically with Escape key
                // No manual handling needed for backstage view

                if (e.Control)
                {
                    switch (e.KeyCode)
                    {
                        case Keys.Home:
                            NavigateToFirst();
                            e.Handled = true;
                            break;
                        case Keys.End:
                            NavigateToLast();
                            e.Handled = true;
                            break;
                    }
                }
                else
                {
                    switch (e.KeyCode)
                    {
                        case Keys.PageUp:
                            NavigateToPrevious();
                            e.Handled = true;
                            break;
                        case Keys.PageDown:
                            NavigateToNext();
                            e.Handled = true;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in keyboard shortcut handler: {ex.Message}");
            }
        }

        #endregion

        #region Navigation Methods

        private void NavigateToFirst()
        {
            try
            {
                var estimate = EstimateFormNavigation.NavigateToFirst();
                if (estimate != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(estimate);
                    EstimateFormDataMapper.LoadEstimateToForm(this, estimate);
                    currentEstimate = estimate;
                    EstimateFormNavigation.UpdateNavigationPosition(this, currentEstimate);
                    EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                    UpdateToggleState(); // Ensure toggle state is correct
                }
                else
                {
                    MessageBox.Show("No estimates found in the database.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to first estimate: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NavigateToPrevious()
        {
            try
            {
                if (currentEstimate?.Id == null || currentEstimate.Id <= 0)
                {
                    MessageBox.Show("Please select an estimate first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var estimate = EstimateFormNavigation.NavigateToPrevious(currentEstimate.Id);
                if (estimate != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(estimate);
                    EstimateFormDataMapper.LoadEstimateToForm(this, estimate);
                    currentEstimate = estimate;
                    EstimateFormNavigation.UpdateNavigationPosition(this, currentEstimate);
                    EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                    UpdateToggleState(); // Ensure toggle state is correct
                }
                else
                {
                    MessageBox.Show("You are already at the first estimate.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to previous estimate: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NavigateToNext()
        {
            try
            {
                if (currentEstimate?.Id == null || currentEstimate.Id <= 0)
                {
                    MessageBox.Show("Please select an estimate first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var estimate = EstimateFormNavigation.NavigateToNext(currentEstimate.Id);
                if (estimate != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(estimate);
                    EstimateFormDataMapper.LoadEstimateToForm(this, estimate);
                    currentEstimate = estimate;
                    EstimateFormNavigation.UpdateNavigationPosition(this, currentEstimate);
                    EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                    UpdateToggleState(); // Ensure toggle state is correct
                }
                else
                {
                    MessageBox.Show("You are already at the last estimate.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to next estimate: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NavigateToLast()
        {
            try
            {
                var estimate = EstimateFormNavigation.NavigateToLast();
                if (estimate != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(estimate);
                    EstimateFormDataMapper.LoadEstimateToForm(this, estimate);
                    currentEstimate = estimate;
                    EstimateFormNavigation.UpdateNavigationPosition(this, currentEstimate);
                    EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                    UpdateToggleState(); // Ensure toggle state is correct
                }
                else
                {
                    MessageBox.Show("No estimates found in the database.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to last estimate: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region CRUD Operations

        private void CreateNewEstimate()
        {
            EstimateFormEventHandlers.CreateNewEstimate(this, ref currentEstimate, ref isEditMode);
        }

        private void EditCurrentEstimate()
        {
            EstimateFormEventHandlers.EditCurrentEstimate(this, currentEstimate, ref isEditMode);
        }

        private void SaveCurrentEstimate()
        {
            EstimateFormEventHandlers.SaveCurrentEstimate(this, ref currentEstimate, ref isEditMode);
        }

        private void CancelCurrentOperation()
        {
            EstimateFormEventHandlers.CancelCurrentOperation(this, ref currentEstimate, ref isEditMode);
        }

        private void DeleteCurrentEstimate()
        {
            EstimateFormEventHandlers.DeleteCurrentEstimate(this, ref currentEstimate, ref isEditMode);
        }

        #endregion

        #region Uppercase Conversion

        /// <summary>
        /// Sets up real-time uppercase conversion for Part Number and Description fields
        /// </summary>
        private void SetupUppercaseConversion()
        {
            try
            {
                // Hook ShownEditor event for uppercase conversion
                GridView1.ShownEditor += GridView1_ShownEditor;
                Debug.WriteLine("Uppercase conversion setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up uppercase conversion: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles when an editor is shown - sets up uppercase conversion for text fields
        /// </summary>
        private void GridView1_ShownEditor(object sender, EventArgs e)
        {
            try
            {
                if (GridView1.FocusedColumn?.FieldName == "PartNumber" || GridView1.FocusedColumn?.FieldName == "Description")
                {
                    var editor = GridView1.ActiveEditor as DevExpress.XtraEditors.TextEdit;
                    if (editor != null)
                    {
                        // Remove existing handlers to prevent duplicates
                        editor.KeyPress -= Editor_KeyPress;
                        editor.TextChanged -= Editor_TextChanged;

                        // Add event handlers
                        editor.KeyPress += Editor_KeyPress;
                        editor.TextChanged += Editor_TextChanged;

                        Debug.WriteLine($"Uppercase conversion enabled for {GridView1.FocusedColumn.FieldName}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView1_ShownEditor: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles KeyPress for real-time typing - converts characters to uppercase
        /// </summary>
        private void Editor_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                e.KeyChar = char.ToUpper(e.KeyChar); // Convert before showing
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Editor_KeyPress: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles TextChanged for pasted content - converts text to uppercase while preserving cursor position
        /// </summary>
        private void Editor_TextChanged(object sender, EventArgs e)
        {
            try
            {
                var edit = sender as DevExpress.XtraEditors.TextEdit;
                if (edit != null)
                {
                    int cursor = edit.SelectionStart;
                    string newText = edit.Text.ToUpper();
                    if (edit.Text != newText)
                    {
                        edit.Text = newText;
                        edit.SelectionStart = cursor; // Retain caret position
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Editor_TextChanged: {ex.Message}");
            }
        }

        #endregion

        #region Grid Operations

        private void AddNewGridRow()
        {
            try
            {
                EstimateFormGridManager.AddNewRow(this, isEditMode);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding new row: {ex.Message}", "Grid Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Form Events

        /// <summary>
        /// Handles the form load event - required by Designer.cs
        /// </summary>
        private void EstimateForm_Load(object sender, EventArgs e)
        {
            // Form initialization is handled in InitializeForm() called from constructor
            // This method is required by the Designer.cs file
        }

        /// <summary>
        /// Handles the group box enter event - required by Designer.cs
        /// </summary>
        private void gbEstiamateHeader_Enter(object sender, EventArgs e)
        {
            // This method is required by the Designer.cs file
            // No specific action needed for group box enter event
        }

        /// <summary>
        /// Handles the ribbon control click event - required by Designer.cs
        /// </summary>
        private void RibbonControl_Click(object sender, EventArgs e)
        {
            // This method is required by the Designer.cs file
            // No specific action needed for ribbon control click event
        }

        /// <summary>
        /// Handles the status toggle switch changed event
        /// RIGHT position (checked) = Closed = true in DB, LEFT position (unchecked) = Active = false in DB
        /// </summary>
        private void BarToggleSwitchItem1_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                if (currentEstimate == null) return;

                // RIGHT (checked) = Closed (true), LEFT (unchecked) = Active (false)
                bool newStatus = tglStatus.Checked;
                currentEstimate.Status = newStatus;

                // Update caption: true = Closed, false = Active
                tglStatus.Caption = newStatus ? "Closed" : "Active";

                // Save status to database using simple SQL update
                bool saved = UpdateEstimateStatusInDatabase(currentEstimate.Id, newStatus);

                if (saved)
                {
                    // If closed (true), disable editing controls and Edit/Delete buttons
                    if (newStatus)
                    {
                        EstimateFormHelper.EnableControls(this, false);
                        EstimateFormGridManager.SetGridEditMode(this, false);

                        // Disable Edit and Delete buttons specifically when closed
                        BarButtonItemEdit.Enabled = false;
                        BarButtonItemDelete.Enabled = false;
                        BarButtonItemSave.Enabled = false;
                        BarButtonItemCancel.Enabled = false;
                        BarButtonItemAddRow.Enabled = false;

                        Debug.WriteLine("Estimate is closed - Edit and Delete buttons disabled");
                    }
                    else
                    {
                        // If active (false), enable normal button states
                        EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                        Debug.WriteLine("Estimate is active - Normal button states restored");
                    }
                }
                else
                {
                    // Revert if save failed - temporarily disable event handler
                    tglStatus.CheckedChanged -= BarToggleSwitchItem1_CheckedChanged;
                    currentEstimate.Status = !newStatus;
                    tglStatus.Checked = !newStatus;
                    tglStatus.Caption = !newStatus ? "Closed" : "Active";
                    tglStatus.CheckedChanged += BarToggleSwitchItem1_CheckedChanged;
                    MessageBox.Show("Failed to save status change", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                Debug.WriteLine($"Toggle: {(newStatus ? "Closed" : "Active")}, Status in DB: {newStatus}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in toggle: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates only the status field in the database
        /// </summary>
        private bool UpdateEstimateStatusInDatabase(int estimateId, bool status)
        {
            try
            {
                using (var conn = ProManage.Modules.Connections.DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    string sql = "UPDATE estimateheader SET status = @status, modify_at = CURRENT_TIMESTAMP WHERE id = @estimate_id";

                    using (var cmd = new Npgsql.NpgsqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@status", status);
                        cmd.Parameters.AddWithValue("@estimate_id", estimateId);

                        int rowsAffected = cmd.ExecuteNonQuery();
                        Debug.WriteLine($"Status update: {rowsAffected} rows affected");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Updates the toggle button state based on current estimate status
        /// </summary>
        public void UpdateToggleState()
        {
            try
            {
                if (currentEstimate == null)
                {
                    // Temporarily disable event handler
                    tglStatus.CheckedChanged -= BarToggleSwitchItem1_CheckedChanged;

                    // No estimate loaded - disable toggle
                    tglStatus.Enabled = false;
                    tglStatus.Caption = "No Estimate";
                    tglStatus.Checked = false;

                    // Re-enable event handler
                    tglStatus.CheckedChanged += BarToggleSwitchItem1_CheckedChanged;
                    return;
                }

                // Temporarily disable event handler to prevent triggering during update
                tglStatus.CheckedChanged -= BarToggleSwitchItem1_CheckedChanged;

                // Enable toggle
                tglStatus.Enabled = true;

                // Update toggle state and caption based on estimate status
                // Direct mapping: Closed (true) = RIGHT (checked), Active (false) = LEFT (unchecked)
                tglStatus.Checked = currentEstimate.Status;
                tglStatus.Caption = currentEstimate.Status ? "Closed" : "Active";

                // Re-enable event handler
                tglStatus.CheckedChanged += BarToggleSwitchItem1_CheckedChanged;

                // Update button states based on estimate status
                if (currentEstimate.Status) // Closed
                {
                    // Disable Edit and Delete buttons when estimate is closed
                    BarButtonItemEdit.Enabled = false;
                    BarButtonItemDelete.Enabled = false;
                    BarButtonItemSave.Enabled = false;
                    BarButtonItemCancel.Enabled = false;
                    BarButtonItemAddRow.Enabled = false;
                    Debug.WriteLine("Estimate is closed - Edit and Delete buttons disabled during navigation");
                }
                else // Active
                {
                    // Enable normal button states when estimate is active
                    EstimateFormHelper.UpdateButtonStates(this, isEditMode);
                    Debug.WriteLine("Estimate is active - Normal button states applied during navigation");
                }

                Debug.WriteLine($"Toggle updated - Status: {currentEstimate.Status}, Caption: {tglStatus.Caption}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating toggle state: {ex.Message}");
                // Make sure to re-enable event handler even if there was an error
                try
                {
                    tglStatus.CheckedChanged += BarToggleSwitchItem1_CheckedChanged;
                }
                catch { }
            }
        }



        #endregion

        #region Print Operations













        /// <summary>
        /// Prints the current estimate directly without showing preview
        /// </summary>
        private void PrintCurrentEstimate()
        {
            try
            {
                Debug.WriteLine("=== PrintCurrentEstimate: Starting ===");

                // Check if we have a current estimate
                if (currentEstimate == null)
                {
                    MessageBox.Show("No estimate is currently loaded. Please select an estimate first.",
                        "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Generate the report using the report service
                var report = ProManage.Modules.Reports.EstimateReportService.CreateEstimateReport(this);

                if (report == null)
                {
                    MessageBox.Show("Failed to create report. Please check the data and try again.",
                        "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Create the document before printing
                report.CreateDocument();

                // Print the report directly
                report.Print();

                Debug.WriteLine($"=== PrintCurrentEstimate: Completed for estimate {currentEstimate.EstimateNo} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PrintCurrentEstimate: {ex.Message}");
                MessageBox.Show($"Error printing estimate: {ex.Message}", "Print Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Shows the print preview form with the current estimate report
        /// </summary>
        private void ShowPrintPreview()
        {
            try
            {
                Debug.WriteLine("=== ShowPrintPreview: Starting ===");

                // Check if we have a current estimate
                if (currentEstimate == null)
                {
                    MessageBox.Show("No estimate is currently loaded. Please select an estimate first.",
                        "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"Generating print preview for estimate: {currentEstimate.EstimateNo}");

                // Generate the report using the existing report service
                var report = ProManage.Modules.Reports.EstimateReportService.CreateEstimateReport(this);

                if (report == null)
                {
                    MessageBox.Show("Failed to create report. Please check the data and try again.",
                        "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                Debug.WriteLine("Report generated successfully, creating print preview form...");

                // Create and show the print preview form
                var printPreviewForm = new ProManage.Forms.UserControlsForms.PrintPreviewForm();

                // Load the report with a descriptive title
                string reportTitle = $"Estimate Print Preview - {currentEstimate.EstimateNo}";
                printPreviewForm.LoadReport(report, reportTitle);

                // Show the form as a modal dialog
                printPreviewForm.ShowDialog(this);

                Debug.WriteLine("=== ShowPrintPreview: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ShowPrintPreview: {ex.Message}");
                MessageBox.Show($"Error showing print preview: {ex.Message}", "Print Preview Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        private void barButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // This method is called from the designer event assignment
            // Call our main print preview method
            ShowPrintPreview();
        }
    }
}