using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Data.ParametersForm;
using System.Diagnostics;

namespace ProManage.Forms
{
    /// <summary>
    /// Parameter Entry Form - Popup dialog for adding new parameters or editing existing ones
    /// </summary>
    public partial class ParamEntryForm : Form
    {
        /// <summary>
        /// Gets the newly created parameter ID after successful save
        /// </summary>
        public int NewParameterId { get; private set; }

        /// <summary>
        /// Gets or sets the parameter being edited (null for new parameters)
        /// </summary>
        private ParametersFormModel EditingParameter { get; set; }

        /// <summary>
        /// Gets whether this form is in edit mode
        /// </summary>
        public bool IsEditMode => EditingParameter != null;

        public ParamEntryForm()
        {
            InitializeComponent();
            SetupForm();
            SetupParameterTypeComboBox();
        }

        /// <summary>
        /// Sets up form properties and behavior
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // Form properties
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.ShowInTaskbar = false;
                this.StartPosition = FormStartPosition.CenterParent;
                this.Text = "Add New Parameter";
                this.Size = new Size(400, 220);

                // Set tab order
                txtParameterCode.TabIndex = 0;
                comboBoxEdit1.TabIndex = 1;
                txtParameterValue.TabIndex = 2;
                txtPurpose.TabIndex = 3;
                btnSave.TabIndex = 4;
                btnCancel.TabIndex = 5;

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine("ParamEntryForm setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupForm: {ex.Message}");
                MessageBox.Show($"Error setting up form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up the parameter type combo box with available parameter types
        /// </summary>
        private void SetupParameterTypeComboBox()
        {
            try
            {
                Debug.WriteLine("Setting up parameter type combo box");

                // Clear existing items
                comboBoxEdit1.Properties.Items.Clear();

                // Get all parameter types and add them to the combo box
                var parameterTypes = ParameterTypeHelper.GetAllTypes();
                foreach (var type in parameterTypes)
                {
                    comboBoxEdit1.Properties.Items.Add(new ParameterTypeItem(type.Key, type.Value));
                }

                // Set default selection to String
                if (comboBoxEdit1.Properties.Items.Count > 0)
                {
                    comboBoxEdit1.SelectedIndex = 0; // String is first (index 0)
                }

                // DevExpress ComboBoxEdit doesn't use DisplayMember/ValueMember like standard ComboBox
                // Items are already added as ParameterTypeItem objects, so we can access them directly

                // Add event handler for selection change
                comboBoxEdit1.SelectedIndexChanged += ComboBoxEdit1_SelectedIndexChanged;

                Debug.WriteLine($"Parameter type combo box setup completed with {comboBoxEdit1.Properties.Items.Count} items");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up parameter type combo box: {ex.Message}");
                MessageBox.Show($"Error setting up parameter types: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles parameter type selection change
        /// </summary>
        private void ComboBoxEdit1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    Debug.WriteLine($"Parameter type changed to: {selectedItem.DisplayName}");

                    // Update the parameter value placeholder/hint based on selected type
                    string sampleValue = ParameterTypeHelper.GetSampleValue(selectedItem.ParameterType);
                    SetTextBoxPlaceholder(txtParameterValue, $"e.g., {sampleValue}");

                    // Validate current value if not empty
                    if (!string.IsNullOrWhiteSpace(txtParameterValue.Text))
                    {
                        ValidateParameterValue();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling parameter type change: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates the parameter value against the selected type
        /// </summary>
        private bool ValidateParameterValue()
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    string value = txtParameterValue.Text.Trim();
                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        if (!ParameterTypeHelper.ValidateValue(value, selectedItem.ParameterType, out string errorMessage))
                        {
                            // Show validation error
                            MessageBox.Show($"Invalid value for {selectedItem.DisplayName} type: {errorMessage}",
                                "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtParameterValue.Focus();
                            return false;
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating parameter value: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets up the form for editing an existing parameter
        /// </summary>
        /// <param name="parameter">The parameter to edit</param>
        public void SetupForEdit(ParametersFormModel parameter)
        {
            try
            {
                Debug.WriteLine($"=== SetupForEdit: Setting up form for parameter ID {parameter.Id} ===");

                // Store the parameter being edited
                EditingParameter = parameter;

                // Change form title
                this.Text = "Edit Parameter";

                // Pre-populate the fields
                txtParameterCode.Text = parameter.ParameterCode ?? "";
                txtParameterValue.Text = parameter.ParameterValue ?? "";
                txtPurpose.Text = parameter.Purpose ?? "";

                // Set the parameter type in the combo box
                SetSelectedParameterType(parameter.ParameterType);

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine($"Form setup for editing parameter: {parameter.ParameterCode}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up form for edit: {ex.Message}");
                MessageBox.Show($"Error setting up edit form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Save button click - validates and saves parameter
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Save button clicked ===");

                // Validate input fields
                if (!ValidateInput())
                {
                    return; // Validation failed, stay on form
                }

                if (IsEditMode)
                {
                    // Update existing parameter
                    EditingParameter.ParameterCode = txtParameterCode.Text.Trim();
                    EditingParameter.ParameterValue = txtParameterValue.Text.Trim();
                    EditingParameter.ParameterType = GetSelectedParameterType();
                    EditingParameter.Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim();
                    EditingParameter.ModifiedAt = DateTime.Now;

                    // Validate model
                    if (!EditingParameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Update in database
                    bool success = ParametersFormRepository.UpdateParameter(EditingParameter);
                    if (!success)
                    {
                        MessageBox.Show("Failed to update parameter. Please try again.", "Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    Debug.WriteLine($"Parameter updated successfully: ID {EditingParameter.Id}");

                    // Show success message
                    MessageBox.Show("Parameter updated successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Create new parameter
                    var parameter = new ParametersFormModel
                    {
                        ParameterCode = txtParameterCode.Text.Trim(),
                        ParameterValue = txtParameterValue.Text.Trim(),
                        ParameterType = GetSelectedParameterType(),
                        Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim()
                    };

                    // Validate model
                    if (!parameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Save to database
                    int newId = ParametersFormRepository.InsertParameter(parameter);
                    NewParameterId = newId;

                    Debug.WriteLine($"Parameter saved successfully with ID: {newId}");

                    // Show success message
                    MessageBox.Show("Parameter saved successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Close form with OK result
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving parameter: {ex.Message}");
                MessageBox.Show($"Error saving parameter: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click - closes form without saving
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Cancel button clicked");
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button: {ex.Message}");
                this.Close(); // Force close even if error
            }
        }

        /// <summary>
        /// Validates input fields before saving
        /// </summary>
        /// <returns>True if validation passes, false otherwise</returns>
        private bool ValidateInput()
        {
            try
            {
                // Check parameter code (mandatory)
                if (string.IsNullOrWhiteSpace(txtParameterCode.Text))
                {
                    MessageBox.Show("Parameter Code is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter code length
                if (txtParameterCode.Text.Trim().Length > 100)
                {
                    MessageBox.Show("Parameter Code cannot exceed 100 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter value (mandatory)
                if (string.IsNullOrWhiteSpace(txtParameterValue.Text))
                {
                    MessageBox.Show("Parameter Value is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterValue.Focus();
                    return false;
                }

                // Check parameter value length
                if (txtParameterValue.Text.Trim().Length > 255)
                {
                    MessageBox.Show("Parameter Value cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterValue.Focus();
                    return false;
                }

                // Check purpose length (optional field)
                if (!string.IsNullOrWhiteSpace(txtPurpose.Text) && txtPurpose.Text.Trim().Length > 255)
                {
                    MessageBox.Show("Purpose cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPurpose.Focus();
                    return false;
                }

                // Validate parameter value against selected type
                if (!ValidateParameterValue())
                {
                    return false; // Validation failed
                }

                // Check parameter code uniqueness
                string parameterCode = txtParameterCode.Text.Trim();
                int? excludeId = IsEditMode ? EditingParameter.Id : (int?)null;

                if (ParametersFormRepository.CheckParameterCodeExists(parameterCode, excludeId))
                {
                    MessageBox.Show($"Parameter Code '{parameterCode}' already exists. Please use a different code.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                Debug.WriteLine("Input validation passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateInput: {ex.Message}");
                MessageBox.Show($"Error validating input: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Handles Enter key press to trigger Save button
        /// </summary>
        protected override bool ProcessDialogKey(Keys keyData)
        {
            if (keyData == Keys.Enter && !txtPurpose.Focused)
            {
                BtnSave_Click(this, EventArgs.Empty);
                return true;
            }
            else if (keyData == Keys.Escape)
            {
                BtnCancel_Click(this, EventArgs.Empty);
                return true;
            }

            return base.ProcessDialogKey(keyData);
        }

        /// <summary>
        /// Sets the selected parameter type in the combo box
        /// </summary>
        /// <param name="parameterType">The parameter type to select</param>
        private void SetSelectedParameterType(ParameterType parameterType)
        {
            try
            {
                for (int i = 0; i < comboBoxEdit1.Properties.Items.Count; i++)
                {
                    if (comboBoxEdit1.Properties.Items[i] is ParameterTypeItem item && item.ParameterType == parameterType)
                    {
                        comboBoxEdit1.SelectedIndex = i;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting selected parameter type: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the currently selected parameter type
        /// </summary>
        /// <returns>The selected parameter type</returns>
        private ParameterType GetSelectedParameterType()
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    return selectedItem.ParameterType;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting selected parameter type: {ex.Message}");
            }

            // Default to String if no selection or error
            return ParameterType.String;
        }

        /// <summary>
        /// Sets placeholder text for a TextBox control (compatible with .NET Framework)
        /// </summary>
        /// <param name="textBox">The TextBox control</param>
        /// <param name="placeholderText">The placeholder text to display</param>
        private void SetTextBoxPlaceholder(TextBox textBox, string placeholderText)
        {
            // Store the placeholder text as a tag for reference
            textBox.Tag = placeholderText;

            // Set initial placeholder appearance if the textbox is empty
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = placeholderText;
                textBox.ForeColor = Color.Gray;
            }

            // Remove existing event handlers to avoid duplicates
            textBox.Enter -= TextBox_Enter;
            textBox.Leave -= TextBox_Leave;

            // Add event handlers for placeholder behavior
            textBox.Enter += TextBox_Enter;
            textBox.Leave += TextBox_Leave;
        }

        /// <summary>
        /// Handles TextBox Enter event for placeholder behavior
        /// </summary>
        private void TextBox_Enter(object sender, EventArgs e)
        {
            if (sender is TextBox textBox && textBox.Tag is string placeholderText)
            {
                if (textBox.Text == placeholderText && textBox.ForeColor == Color.Gray)
                {
                    textBox.Text = "";
                    textBox.ForeColor = Color.Black;
                }
            }
        }

        /// <summary>
        /// Handles TextBox Leave event for placeholder behavior
        /// </summary>
        private void TextBox_Leave(object sender, EventArgs e)
        {
            if (sender is TextBox textBox && textBox.Tag is string placeholderText)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    textBox.Text = placeholderText;
                    textBox.ForeColor = Color.Gray;
                }
            }
        }
    }

    /// <summary>
    /// Helper class for parameter type combo box items
    /// </summary>
    public class ParameterTypeItem
    {
        public ParameterType ParameterType { get; set; }
        public string DisplayName { get; set; }

        public ParameterTypeItem(ParameterType parameterType, string displayName)
        {
            ParameterType = parameterType;
            DisplayName = displayName;
        }

        public override string ToString()
        {
            return DisplayName;
        }
    }
}
