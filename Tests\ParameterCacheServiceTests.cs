// ParameterCacheService Tests - Basic functionality test
// Usage: Simple test to verify ParameterCacheService functionality

using System;
using System.Diagnostics;
using System.Windows.Forms;
using ProManage.Modules.Services;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class for ParameterCacheService functionality
    /// This is not a unit test framework test, but a basic verification
    /// </summary>
    public static class ParameterCacheServiceTests
    {
        /// <summary>
        /// Runs basic tests for ParameterCacheService
        /// </summary>
        public static void RunBasicTests()
        {
            try
            {
                Debug.WriteLine("=== ParameterCacheService Basic Tests Starting ===");

                // Test 1: Check if service is accessible
                TestServiceAccessibility();

                // Test 2: Test parameter retrieval with defaults
                TestParameterRetrievalWithDefaults();

                // Test 3: Test parameter existence checking
                TestParameterExistenceChecking();

                // Test 4: Test cache status
                TestCacheStatus();

                Debug.WriteLine("=== ParameterCacheService Basic Tests Completed Successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== ParameterCacheService Tests Failed: {ex.Message} ===");
                MessageBox.Show($"Parameter cache tests failed: {ex.Message}", "Test Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test 1: Verify service singleton accessibility
        /// </summary>
        private static void TestServiceAccessibility()
        {
            Debug.WriteLine("Test 1: Service Accessibility");

            var instance1 = ParameterCacheService.Instance;
            var instance2 = ParameterCacheService.Instance;

            if (instance1 == instance2)
            {
                Debug.WriteLine("✓ Singleton pattern working correctly");
            }
            else
            {
                throw new Exception("Singleton pattern failed - different instances returned");
            }

            if (instance1 != null)
            {
                Debug.WriteLine("✓ Service instance is accessible");
            }
            else
            {
                throw new Exception("Service instance is null");
            }
        }

        /// <summary>
        /// Test 2: Test parameter retrieval with default values
        /// </summary>
        private static void TestParameterRetrievalWithDefaults()
        {
            Debug.WriteLine("Test 2: Parameter Retrieval with Defaults");

            // Test getting parameter with default value
            string currency = ParameterCacheService.Instance.GetParameter("CURRENCY", "USD");
            Debug.WriteLine($"Currency parameter: {currency}");

            if (!string.IsNullOrEmpty(currency))
            {
                Debug.WriteLine("✓ Parameter retrieval with default working");
            }
            else
            {
                throw new Exception("Parameter retrieval returned null/empty");
            }

            // Test getting non-existent parameter with default
            string nonExistent = ParameterCacheService.Instance.GetParameter("NON_EXISTENT_PARAM", "DEFAULT_VALUE");
            if (nonExistent == "DEFAULT_VALUE")
            {
                Debug.WriteLine("✓ Default value fallback working correctly");
            }
            else
            {
                Debug.WriteLine($"⚠ Default value fallback may not be working as expected. Got: {nonExistent}");
            }

            // Test getting parameter without default
            string withoutDefault = ParameterCacheService.Instance.GetParameter("CURRENCY");
            Debug.WriteLine($"Parameter without default: {withoutDefault ?? "NULL"}");
        }

        /// <summary>
        /// Test 3: Test parameter existence checking
        /// </summary>
        private static void TestParameterExistenceChecking()
        {
            Debug.WriteLine("Test 3: Parameter Existence Checking");

            // Test HasParameter method
            bool hasCurrency = ParameterCacheService.Instance.HasParameter("CURRENCY");
            Debug.WriteLine($"Has CURRENCY parameter: {hasCurrency}");

            bool hasNonExistent = ParameterCacheService.Instance.HasParameter("NON_EXISTENT_PARAM");
            Debug.WriteLine($"Has NON_EXISTENT_PARAM parameter: {hasNonExistent}");

            if (!hasNonExistent)
            {
                Debug.WriteLine("✓ HasParameter correctly identifies non-existent parameters");
            }
            else
            {
                Debug.WriteLine("⚠ HasParameter may have issues with non-existent parameters");
            }

            // Test with null/empty parameter codes
            bool hasNull = ParameterCacheService.Instance.HasParameter(null);
            bool hasEmpty = ParameterCacheService.Instance.HasParameter("");
            bool hasWhitespace = ParameterCacheService.Instance.HasParameter("   ");

            if (!hasNull && !hasEmpty && !hasWhitespace)
            {
                Debug.WriteLine("✓ HasParameter correctly handles null/empty/whitespace input");
            }
            else
            {
                Debug.WriteLine("⚠ HasParameter may have issues with null/empty/whitespace input");
            }
        }

        /// <summary>
        /// Test 4: Test cache status and properties
        /// </summary>
        private static void TestCacheStatus()
        {
            Debug.WriteLine("Test 4: Cache Status");

            // Test IsLoaded property
            bool isLoaded = ParameterCacheService.Instance.IsLoaded;
            Debug.WriteLine($"Cache is loaded: {isLoaded}");

            // Test ParameterCount property
            int parameterCount = ParameterCacheService.Instance.ParameterCount;
            Debug.WriteLine($"Parameter count: {parameterCount}");

            if (parameterCount >= 0)
            {
                Debug.WriteLine("✓ Parameter count is valid");
            }
            else
            {
                Debug.WriteLine("⚠ Parameter count is negative");
            }

            // Test LastCacheUpdate property
            DateTime lastUpdate = ParameterCacheService.Instance.LastCacheUpdate;
            Debug.WriteLine($"Last cache update: {lastUpdate}");

            if (lastUpdate != DateTime.MinValue)
            {
                Debug.WriteLine("✓ Last cache update timestamp is set");
            }
            else
            {
                Debug.WriteLine("⚠ Last cache update timestamp is not set");
            }
        }

        /// <summary>
        /// Test cache refresh functionality
        /// </summary>
        public static void TestCacheRefresh()
        {
            try
            {
                Debug.WriteLine("=== Testing Cache Refresh ===");

                int countBefore = ParameterCacheService.Instance.ParameterCount;
                DateTime updateBefore = ParameterCacheService.Instance.LastCacheUpdate;

                Debug.WriteLine($"Before refresh - Count: {countBefore}, Last Update: {updateBefore}");

                // Attempt to refresh cache
                bool refreshResult = ParameterCacheService.Instance.RefreshCache();
                Debug.WriteLine($"Refresh result: {refreshResult}");

                int countAfter = ParameterCacheService.Instance.ParameterCount;
                DateTime updateAfter = ParameterCacheService.Instance.LastCacheUpdate;

                Debug.WriteLine($"After refresh - Count: {countAfter}, Last Update: {updateAfter}");

                if (refreshResult)
                {
                    Debug.WriteLine("✓ Cache refresh completed successfully");
                }
                else
                {
                    Debug.WriteLine("⚠ Cache refresh returned false - may indicate database connectivity issues");
                }

                Debug.WriteLine("=== Cache Refresh Test Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Cache refresh test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Comprehensive test that can be called from a form or main application
        /// </summary>
        public static void RunComprehensiveTest()
        {
            try
            {
                Debug.WriteLine("=== Comprehensive ParameterCacheService Test ===");

                // Run basic tests
                RunBasicTests();

                // Test cache refresh
                TestCacheRefresh();

                // Test common usage patterns
                TestCommonUsagePatterns();

                Debug.WriteLine("=== All Tests Completed Successfully ===");

                MessageBox.Show("ParameterCacheService tests completed successfully! Check Debug output for details.", 
                    "Test Results", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Comprehensive test failed: {ex.Message}");
                MessageBox.Show($"ParameterCacheService tests failed: {ex.Message}", 
                    "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test common usage patterns
        /// </summary>
        private static void TestCommonUsagePatterns()
        {
            Debug.WriteLine("=== Testing Common Usage Patterns ===");

            // Pattern 1: Get parameter with default
            string currency = ParameterCacheService.Instance.GetParameter("CURRENCY", "USD");
            Debug.WriteLine($"Pattern 1 - Currency: {currency}");

            // Pattern 2: Check existence before getting
            if (ParameterCacheService.Instance.HasParameter("TAX_RATE"))
            {
                string taxRate = ParameterCacheService.Instance.GetParameter("TAX_RATE");
                Debug.WriteLine($"Pattern 2 - Tax Rate: {taxRate}");
            }
            else
            {
                Debug.WriteLine("Pattern 2 - TAX_RATE parameter not found");
            }

            // Pattern 3: Safe type conversion
            string rateStr = ParameterCacheService.Instance.GetParameter("TAX_RATE", "0.00");
            if (decimal.TryParse(rateStr, out decimal rate))
            {
                Debug.WriteLine($"Pattern 3 - Parsed tax rate: {rate}");
            }
            else
            {
                Debug.WriteLine($"Pattern 3 - Could not parse tax rate: {rateStr}");
            }

            Debug.WriteLine("=== Common Usage Patterns Test Completed ===");
        }
    }
}
