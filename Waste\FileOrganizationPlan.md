# ProManage-8.0 File Organization Plan

This document provides a comprehensive mapping of the current files in the project, particularly those in the Data, Helpers, and Models folders, to their corresponding modules/forms. It also suggests more descriptive filenames and organizes files by module/feature rather than by folder to make relationships clearer.

## Current Project Structure

The current project is organized in a traditional folder-based structure:

```
/ProManage-8.0
|-- /Data         # Database and data access classes
|-- /Forms        # UI forms
|-- /Helpers      # Utility and helper classes
|-- /Models       # Data models
|-- MainFrame.vb  # Main application container
|-- App.config    # Application configuration
```

## Module-Based Organization

Below is a reorganization of files by module/feature rather than by folder structure.

### 1. Core Application Framework

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| MainFrame.vb | Root | Application container | ApplicationShell.vb | Main MDI container that hosts all other forms as tabs |
| RootNamespace.vb | Root | All modules | NamespaceDefinitions.vb | Establishes the root namespace structure |
| NamespaceSetup.vb | Data | All modules | NamespaceRegistration.vb | Ensures proper namespace registration |
| App.config | Root | All modules | ApplicationConfiguration.xml | Application configuration settings |
| Development.config | Root | Development environment | DevelopmentSettings.xml | Development-specific configuration |

### 2. Database Connection Module

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| ConnectionManager.vb | Data | All database operations | DatabaseConnectionManager.vb | Singleton class managing database connections |
| DatabaseHelper.vb | Data | Database operations | DatabaseUtilities.vb | Helper methods for database operations |
| ConfigurationHelper.vb | Helpers | Database configuration | DatabaseConfigurationManager.vb | Manages database connection settings |
| DatabaseForm.vb | Forms | Database configuration UI | DatabaseConfigurationForm.vb | UI for configuring database connections |

### 3. Authentication Module

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| UserManager.vb | Data | User authentication | UserSessionManager.vb | Manages the current user session |
| User.vb | Models | User data | UserModel.vb | Data model for user information |
| LoginForm.vb | Forms | User authentication UI | UserAuthenticationForm.vb | Login screen for user authentication |

### 4. Estimate Management Module

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| EstimateRepository.vb | Data | EstimateForm | EstimateDataAccess.vb | Data access for estimates |
| EstimateHeader.vb | Models | EstimateForm | EstimateHeaderModel.vb | Data model for estimate header information |
| EstimateDetail.vb | Models | EstimateForm | EstimateLineItemModel.vb | Data model for estimate detail line items |
| EstimateForm.vb | Forms | Estimate UI | EstimateManagementForm.vb | UI for creating and editing estimates |
| SerialNumberHelper.vb | Helpers | EstimateForm | GridSerialNumberManager.vb | Manages serial numbers in grid views |

### 5. SQL Query Module

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| QueryExecutor.vb | Data | SQLQueryForm | SqlQueryExecutor.vb | Executes SQL queries against the database |
| SQLQueryForm.vb | Forms | SQL Query UI | DatabaseQueryForm.vb | UI for executing SQL queries |

### 6. Common Utilities

| Current File | Location | Supports | Suggested Name | Description |
|--------------|----------|----------|----------------|-------------|
| FormControlHelper.vb | Helpers | All forms | FormControlUtilities.vb | Helper methods for form controls |
| ValidationHelper.vb | Helpers | All forms | InputValidationUtilities.vb | Input validation utilities |

## Recommendations for Improved Organization

1. **Consistent Naming Convention**: Adopt a consistent naming pattern where each file clearly indicates its purpose and the module it belongs to.

2. **Module-Based Organization**: Consider reorganizing the project structure to be module-based rather than type-based:

```
/ProManage-8.0
|-- /Core                  # Core application framework
|-- /DatabaseConnection    # Database connection module
|-- /Authentication        # User authentication module
|-- /EstimateManagement    # Estimate management module
|-- /SqlQuery              # SQL query module
|-- /Common                # Common utilities
```

3. **File Relationship Documentation**: Add XML documentation to each file that clearly indicates its relationships to other files.

4. **Namespace Alignment**: Ensure namespaces reflect the module structure (e.g., `ProManage_8.EstimateManagement.Models`).

5. **Interface Extraction**: Consider extracting interfaces for key components to improve testability and maintainability.

## Implementation Plan

1. **Phase 1**: Rename files according to the suggested names while maintaining the current folder structure.
2. **Phase 2**: Gradually refactor the codebase to adopt a module-based organization.
3. **Phase 3**: Update namespaces to reflect the new organization.
4. **Phase 4**: Extract interfaces for key components.

This approach allows for incremental improvements without disrupting the current functionality.
