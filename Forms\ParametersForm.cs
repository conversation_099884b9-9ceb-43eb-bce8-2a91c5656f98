﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using ProManage.Modules.Helpers.ParametersForm;
using ProManage.Modules.Data.ParametersForm;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    public partial class ParametersForm : Form
    {
        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        public DataTable GridDataTable { get; set; }

        public ParametersForm()
        {
            InitializeComponent();
        }

        private void ProgramParameterForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the grid with columns
                ParametersFormHelper.InitializeGrid(this);

                // Load parameters from database
                ParametersFormHelper.LoadParametersToGrid(this);

                // Setup event handlers
                SetupEventHandlers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading Parameters form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up event handlers for ribbon buttons
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // New button event handler
                barButtonItem1.ItemClick += BarButtonItemNew_ItemClick;

                // Edit button event handler (placeholder for future implementation)
                barButtonItem2.ItemClick += BarButtonItemEdit_ItemClick;                // Delete button event handler
                barButtonItem3.ItemClick += BarButtonItemDelete_ItemClick;

                Debug.WriteLine("Event handlers setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles New button click - opens parameter entry popup dialog
        /// </summary>
        private void BarButtonItemNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== New button clicked - Opening ParamEntryForm ===");

                // Create and show the parameter entry popup form
                using (var paramEntryForm = new ParamEntryForm())
                {
                    // Show as modal dialog
                    var result = paramEntryForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        Debug.WriteLine($"Parameter saved successfully with ID: {paramEntryForm.NewParameterId}");

                        // Refresh parameter cache after new parameter addition
                        Debug.WriteLine("Refreshing parameter cache after new parameter addition");
                        bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                        if (cacheRefreshed)
                        {
                            Debug.WriteLine("Parameter cache refreshed successfully");
                        }
                        else
                        {
                            Debug.WriteLine("Warning: Parameter cache refresh failed");
                        }

                        // Refresh the grid to show the new parameter
                        ParametersFormHelper.RefreshParametersGrid(this);

                        Debug.WriteLine("Grid refreshed after new parameter addition");
                    }
                    else
                    {
                        Debug.WriteLine("Parameter entry was cancelled by user");
                    }
                }

                Debug.WriteLine("=== New button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening parameter entry form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click - opens parameter entry popup dialog for editing
        /// </summary>
        private void BarButtonItemEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Edit button clicked ===");

                // Force grid to post any pending edits
                gridView1.PostEditor();
                gridView1.UpdateCurrentRow();

                // Get the selected parameter
                var selectedParameter = ParametersFormHelper.GetSelectedParameter(this);

                if (selectedParameter == null)
                {
                    MessageBox.Show("Please select a parameter to edit by checking the checkbox in the 'Select' column.",
                        "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"Selected parameter for editing: ID {selectedParameter.Id} - {selectedParameter.ParameterCode}");

                // Create and setup the parameter entry form for editing
                using (var paramEntryForm = new ParamEntryForm())
                {
                    // Setup the form for editing
                    paramEntryForm.SetupForEdit(selectedParameter);

                    // Show as modal dialog
                    var result = paramEntryForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        Debug.WriteLine($"Parameter updated successfully: {selectedParameter.ParameterCode}");

                        // Refresh parameter cache after parameter update
                        Debug.WriteLine("Refreshing parameter cache after parameter update");
                        bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                        if (cacheRefreshed)
                        {
                            Debug.WriteLine("Parameter cache refreshed successfully");
                        }
                        else
                        {
                            Debug.WriteLine("Warning: Parameter cache refresh failed");
                        }

                        // Refresh the grid to show the updated parameter
                        ParametersFormHelper.RefreshParametersGrid(this);

                        Debug.WriteLine("Grid refreshed after parameter update");
                    }
                    else
                    {
                        Debug.WriteLine("Parameter edit was cancelled by user");
                    }
                }

                Debug.WriteLine("=== Edit button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button click: {ex.Message}");
                MessageBox.Show($"Error in edit operation: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click - deletes selected parameters
        /// </summary>
        private void BarButtonItemDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Delete button clicked ===");

                // Force grid to post any pending edits
                gridView1.PostEditor();
                gridView1.UpdateCurrentRow();

                // Get the selected parameter
                var selectedParameter = ParametersFormHelper.GetSelectedParameter(this);

                if (selectedParameter == null)
                {
                    MessageBox.Show("Please select a parameter to delete by checking the checkbox in the 'Select' column.",
                        "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"Selected parameter for deletion: ID {selectedParameter.Id} - {selectedParameter.ParameterCode}");

                // Check if this is a new unsaved parameter
                if (selectedParameter.Id <= 0)
                {
                    MessageBox.Show("This parameter has not been saved to the database yet. Use the grid's checkbox to remove it from the list.",
                        "Cannot Delete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show($"Are you sure you want to delete the parameter '{selectedParameter.ParameterCode}'?\n\nThis action cannot be undone.",
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    Debug.WriteLine("Parameter deletion was cancelled by user");
                    return;
                }

                // Delete from database
                var deleteIds = new List<int> { selectedParameter.Id };
                int deletedCount = ParametersFormRepository.DeleteParameters(deleteIds);

                if (deletedCount > 0)
                {
                    Debug.WriteLine($"Parameter deleted successfully from database: {selectedParameter.ParameterCode}");

                    // Refresh parameter cache after parameter deletion
                    Debug.WriteLine("Refreshing parameter cache after parameter deletion");
                    bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                    if (cacheRefreshed)
                    {
                        Debug.WriteLine("Parameter cache refreshed successfully");
                    }
                    else
                    {
                        Debug.WriteLine("Warning: Parameter cache refresh failed");
                    }

                    // Refresh the grid to show updated data
                    ParametersFormHelper.RefreshParametersGrid(this);

                    MessageBox.Show($"Parameter '{selectedParameter.ParameterCode}' deleted successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Debug.WriteLine("Grid refreshed after parameter deletion");
                }
                else
                {
                    MessageBox.Show("Failed to delete parameter. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                Debug.WriteLine("=== Delete button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button click: {ex.Message}");
                MessageBox.Show($"Error deleting parameter: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }        }        /// <summary>
        /// Demonstrates the new typed parameter access methods
        /// This method shows how to use the UnifiedParameterManager for type-safe parameter access
        /// </summary>
        private void DemonstrateTypedParameterAccess()
        {
            try
            {
                Debug.WriteLine("=== Demonstrating Typed Parameter Access Methods ===");

                // Test integer parameter access
                int decimals = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
                Debug.WriteLine($"DECIMALS parameter (int): {decimals}");

                // Test boolean parameter access
                bool enableLogging = UnifiedParameterManager.Instance.GetBool("ENABLE_LOGGING", true);
                Debug.WriteLine($"ENABLE_LOGGING parameter (bool): {enableLogging}");

                // Test decimal parameter access
                decimal taxRate = UnifiedParameterManager.Instance.GetDecimal("TAX_RATE", 5.0m);
                Debug.WriteLine($"TAX_RATE parameter (decimal): {taxRate}");

                // Test double parameter access - UnifiedParameterManager doesn't have GetDouble, use GetDecimal
                decimal multiplier = UnifiedParameterManager.Instance.GetDecimal("MULTIPLIER", 1.0m);
                Debug.WriteLine($"MULTIPLIER parameter (decimal): {multiplier}");

                // Test DateTime parameter access
                DateTime cutoffDate = UnifiedParameterManager.Instance.GetDateTime("CUTOFF_DATE", DateTime.Today);
                Debug.WriteLine($"CUTOFF_DATE parameter (DateTime): {cutoffDate}");

                // Test enum parameter access - UnifiedParameterManager doesn't have GetEnum, use GetString and parse
                string startDayString = UnifiedParameterManager.Instance.GetString("START_DAY", "Monday");
                DayOfWeek startDay = (DayOfWeek)Enum.Parse(typeof(DayOfWeek), startDayString);
                Debug.WriteLine($"START_DAY parameter (enum): {startDay}");

                // Test with logging - UnifiedParameterManager uses GetString for this
                string currency = UnifiedParameterManager.Instance.GetString("CURRENCY", "USD");
                Debug.WriteLine($"CURRENCY parameter: {currency}");

                // Test backward compatibility - ParameterCacheService methods still work
                string originalMethod = ParameterCacheService.Instance.GetParameter("CURRENCY", "USD");
                Debug.WriteLine($"Original GetParameter method still works: {originalMethod}");

                Debug.WriteLine("=== Typed Parameter Access Demonstration Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DemonstrateTypedParameterAccess: {ex.Message}");
            }
        }        /// <summary>
        /// Handles form closing - no longer needs to check for unsaved changes since grid is read-only
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // Grid is now read-only, so no unsaved changes to worry about
                Debug.WriteLine("ParametersForm closing - no unsaved changes to check (grid is read-only)");
                base.OnFormClosing(e);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OnFormClosing: {ex.Message}");
                base.OnFormClosing(e);
            }
        }

        /// <summary>
        /// This method is no longer relevant since the grid is read-only
        /// Kept for backward compatibility but always returns false
        /// </summary>
        /// <returns>Always returns false since grid editing is disabled</returns>
        private bool HasUnsavedChanges()
        {
            // Grid is read-only, so there are never any unsaved changes
            return false;
        }
    }
}
