﻿using System;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Utils;
using ProManage.Forms;
using ProManage.Modules.Helpers;
using ProManage.Modules.Connections;
using ProManage.Modules.Licensing;
using ProManage.Modules.Services;
using Syncfusion.Licensing;

namespace ProManage
{
    internal static class Program
    {        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Ensure System.Resources.Extensions is loaded early
            EnsureSystemResourcesExtensionsLoaded();

            System.Windows.Forms.Application.EnableVisualStyles();
            System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(false);

            // Initialize DevExpress licensing
            InitializeDevExpressLicensing();

            // Initialize Syncfusion licensing
            InitializeSyncfusionLicensing();

            // Initialize database connection
            InitializeDatabaseConnection();

            // Initialize parameter cache service
            InitializeParameterCache();

            // Initialize type references to resolve namespace issues
            ProManage.Services.TypeResolver.InitializeTypeReferences();

            // Start with the login form
            System.Windows.Forms.Application.Run(new LoginForm());
        }        /// <summary>
        /// Ensures that the System.Resources.Extensions assembly is loaded
        /// </summary>
        private static void EnsureSystemResourcesExtensionsLoaded()
        {
            try
            {                // First try to load from the application directory directly
                string assemblyPath = System.IO.Path.Combine(
                    System.IO.Path.GetDirectoryName(System.Windows.Forms.Application.ExecutablePath),
                    "System.Resources.Extensions.dll");

                System.Reflection.Assembly assembly = null;

                if (System.IO.File.Exists(assemblyPath))
                {
                    Debug.WriteLine("Loading System.Resources.Extensions from: " + assemblyPath);
                    assembly = System.Reflection.Assembly.LoadFrom(assemblyPath);
                }
                else
                {
                    // Try alternative methods of loading
                    Debug.WriteLine("System.Resources.Extensions.dll not found in application directory, attempting Assembly.Load");
                    assembly = System.Reflection.Assembly.Load("System.Resources.Extensions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51");
                }

                if (assembly != null)
                {
                    Debug.WriteLine("System.Resources.Extensions loaded successfully: " + assembly.FullName);

                    // Try to create a concrete type instance to ensure the assembly is properly loaded
                    Type resourceWriterType = assembly.GetType("System.Resources.Extensions.PreserializedResourceWriter");
                    if (resourceWriterType != null)
                    {
                        Debug.WriteLine("PreserializedResourceWriter type found");
                    }
                    else
                    {
                        Debug.WriteLine("Warning: PreserializedResourceWriter type not found");
                    }

                    // Register assembly resolver to help with loading issues
                    AppDomain.CurrentDomain.AssemblyResolve += (sender, args) =>
                    {
                        if (args.Name.StartsWith("System.Resources.Extensions", StringComparison.OrdinalIgnoreCase))
                        {
                            Debug.WriteLine("Resolving System.Resources.Extensions via AssemblyResolve event");
                            return assembly;
                        }
                        return null;
                    };
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error loading System.Resources.Extensions: " + ex.Message);
                MessageBox.Show("Failed to load System.Resources.Extensions. Some features may not work correctly.\n\nError: " + ex.Message,
                    "Resource Loading Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Initializes DevExpress licensing and theme system
        /// </summary>
        private static void InitializeDevExpressLicensing()
        {
            try
            {
                // Initialize the theme manager
                ProManage.Services.ThemeManager.Initialize();

                // Check if running in design mode
                if (Modules.Licensing.LicenseManager.UsageMode == Modules.Licensing.LicenseUsageMode.Designtime)
                {
                    Debug.WriteLine("DevExpress running in design mode");
                    return;
                }

                // Check if license is valid (DevExpress validates automatically from licenses.licx)
                // This is just for logging purposes
                if (AppDomain.CurrentDomain.GetAssemblies().Any(a => a.FullName.Contains("DevExpress")))
                {
                    Debug.WriteLine("DevExpress assemblies loaded successfully");
                }
                else
                {
                    Debug.WriteLine("Warning: DevExpress assemblies not found");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing DevExpress licensing: {ex.Message}");
                MessageBox.Show($"Error initializing DevExpress components: {ex.Message}\n\nThe application will continue to run but some features may be limited.",
                    "Licensing Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Initializes Syncfusion licensing
        /// </summary>
        private static void InitializeSyncfusionLicensing()
        {
            try
            {
                // Register Syncfusion license
                // This is the Community License key - replace with your actual license key in production
                SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1NNaF5cXmBCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXpfcnRTRWdeUkdxWUZWYUA=");

                Debug.WriteLine("Syncfusion license registered successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error registering Syncfusion license: {ex.Message}");
                MessageBox.Show($"Error initializing Syncfusion components: {ex.Message}\n\nThe application will continue to run but some features may be limited.",
                    "Licensing Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }        /// <summary>
        /// Initializes the database connection if it is configured
        /// </summary>
        private static void InitializeDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("Initializing database connection...");

                // Check if database is configured
                if (DatabaseConnectionManager.Instance.IsConfigured)
                {
                    // Try to open the connection
                    if (DatabaseConnectionManager.Instance.OpenConnection())
                    {
                        Debug.WriteLine("Database connection established successfully");

                        // Start the connection monitoring to maintain persistence
                        DatabaseConnectionManager.Instance.StartConnectionMonitoring();
                    }
                    else
                    {
                        Debug.WriteLine($"Failed to connect to database: {DatabaseConnectionManager.Instance.LastError}");

                        // Show an error message and prompt for database configuration if connection fails
                        MessageBox.Show(
                            $"Unable to connect to the database. You will need to configure the database connection before using the application.\n\nError: {DatabaseConnectionManager.Instance.LastError}",
                            "Database Connection Failed",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);

                        // Show database configuration form immediately
                        using (var dbForm = new DatabaseForm())
                        {
                            dbForm.ShowDialog();
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("Database not configured, user will need to configure it first");

                    // Show a message and prompt for database configuration
                    MessageBox.Show(
                        "No database configuration found. Please configure the database connection before using the application.",
                        "Database Configuration Required",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    // Show database configuration form immediately
                    using (var dbForm = new DatabaseForm())
                    {
                        dbForm.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing database connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Show an error message
                MessageBox.Show(
                    $"An error occurred while initializing the database connection: {ex.Message}",
                    "Database Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initializes the unified parameter manager
        /// </summary>
        private static void InitializeParameterCache()
        {
            try
            {
                Debug.WriteLine("Initializing unified parameter manager...");

                // Initialize the unified parameter manager (replaces multiple parameter services)
                bool managerInitialized = UnifiedParameterManager.Instance.Initialize();

                if (managerInitialized)
                {
                    Debug.WriteLine($"Unified parameter manager initialized successfully with {UnifiedParameterManager.Instance.ParameterCount} parameters");

                    // Log some sample parameter access to verify functionality
                    Debug.WriteLine($"Sample parameter access - Currency: {UnifiedParameterManager.Instance.Currency.Symbol}");
                    Debug.WriteLine($"Sample parameter access - Company: {UnifiedParameterManager.Instance.Company.Name}");

                    // Run quick validation to ensure implementation is working
                    bool validationPassed = ProManage.Tests.ParameterSystemValidationTests.QuickValidation();
                    Debug.WriteLine($"Parameter implementation validation: {(validationPassed ? "PASSED" : "FAILED")}");
                }
                else
                {
                    Debug.WriteLine("Warning: Unified parameter manager initialization failed, using default values");

                    // Show a warning but don't block application startup
                    MessageBox.Show(
                        "Parameter system could not be initialized. Default parameter values will be used.\n\nThis may impact functionality but the application will continue to run.",
                        "Parameter System Warning",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }

                // Keep legacy parameter cache service running during transition (backward compatibility)
                try
                {
                    Debug.WriteLine("Initializing legacy parameter cache service for backward compatibility...");
                    ParameterCacheService.Instance.Initialize();
                }
                catch (Exception legacyEx)
                {
                    Debug.WriteLine($"Legacy parameter cache initialization failed: {legacyEx.Message}");
                    // Don't show error for legacy service - unified manager is primary now
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing unified parameter manager: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Log the error but don't block application startup
                MessageBox.Show(
                    $"An error occurred while initializing the parameter system: {ex.Message}\n\nDefault parameter values will be used.",
                    "Parameter System Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }
        }
    }
}
