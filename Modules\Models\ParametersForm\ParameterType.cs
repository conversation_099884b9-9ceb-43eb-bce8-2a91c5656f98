// Parameter Type Enumeration - Defines the supported parameter data types
// Usage: Used for type-safe parameter entry and validation in the parameter system

using System;
using System.ComponentModel;

namespace ProManage.Modules.Models.ParametersForm
{
    /// <summary>
    /// Enumeration of supported parameter data types
    /// Used for type validation and conversion in the unified parameter system
    /// </summary>
    public enum ParameterType
    {
        /// <summary>
        /// String/Text parameter type - stores text values
        /// </summary>
        [Description("String")]
        String = 1,

        /// <summary>
        /// Integer/Number parameter type - stores whole numbers
        /// </summary>
        [Description("Number")]
        Number = 2,

        /// <summary>
        /// Decimal parameter type - stores decimal numbers with precision
        /// </summary>
        [Description("Decimal")]
        Decimal = 3,

        /// <summary>
        /// Date parameter type - stores date and time values
        /// </summary>
        [Description("Date")]
        Date = 4,

        /// <summary>
        /// Boolean parameter type - stores true/false values
        /// </summary>
        [Description("Boolean")]
        Boolean = 5
    }

    /// <summary>
    /// Helper class for ParameterType enumeration operations
    /// </summary>
    public static class ParameterTypeHelper
    {
        /// <summary>
        /// Gets the display name for a parameter type
        /// </summary>
        /// <param name="parameterType">The parameter type</param>
        /// <returns>Display name for the parameter type</returns>
        public static string GetDisplayName(ParameterType parameterType)
        {
            var field = parameterType.GetType().GetField(parameterType.ToString());
            var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            return attribute?.Description ?? parameterType.ToString();
        }

        /// <summary>
        /// Gets all available parameter types with their display names
        /// </summary>
        /// <returns>Dictionary of parameter types and their display names</returns>
        public static System.Collections.Generic.Dictionary<ParameterType, string> GetAllTypes()
        {
            var types = new System.Collections.Generic.Dictionary<ParameterType, string>();
            
            foreach (ParameterType type in Enum.GetValues(typeof(ParameterType)))
            {
                types.Add(type, GetDisplayName(type));
            }
            
            return types;
        }

        /// <summary>
        /// Validates if a value is compatible with the specified parameter type
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="parameterType">The expected parameter type</param>
        /// <param name="errorMessage">Error message if validation fails</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool ValidateValue(string value, ParameterType parameterType, out string errorMessage)
        {
            errorMessage = null;

            if (string.IsNullOrWhiteSpace(value))
            {
                errorMessage = "Value cannot be empty";
                return false;
            }

            switch (parameterType)
            {
                case ParameterType.String:
                    // String values are always valid
                    return true;

                case ParameterType.Number:
                    if (!int.TryParse(value, out _))
                    {
                        errorMessage = "Value must be a valid whole number";
                        return false;
                    }
                    return true;

                case ParameterType.Decimal:
                    if (!decimal.TryParse(value, out _))
                    {
                        errorMessage = "Value must be a valid decimal number";
                        return false;
                    }
                    return true;

                case ParameterType.Date:
                    if (!DateTime.TryParse(value, out _))
                    {
                        errorMessage = "Value must be a valid date (e.g., MM/dd/yyyy or yyyy-MM-dd)";
                        return false;
                    }
                    return true;

                case ParameterType.Boolean:
                    string lowerValue = value.ToLower().Trim();
                    if (lowerValue != "true" && lowerValue != "false" && 
                        lowerValue != "1" && lowerValue != "0" &&
                        lowerValue != "yes" && lowerValue != "no" &&
                        lowerValue != "y" && lowerValue != "n")
                    {
                        errorMessage = "Value must be true/false, 1/0, yes/no, or y/n";
                        return false;
                    }
                    return true;

                default:
                    errorMessage = "Unknown parameter type";
                    return false;
            }
        }

        /// <summary>
        /// Gets a sample value for the specified parameter type
        /// </summary>
        /// <param name="parameterType">The parameter type</param>
        /// <returns>Sample value string</returns>
        public static string GetSampleValue(ParameterType parameterType)
        {
            switch (parameterType)
            {
                case ParameterType.String:
                    return "Sample text";
                case ParameterType.Number:
                    return "123";
                case ParameterType.Decimal:
                    return "123.45";
                case ParameterType.Date:
                    return DateTime.Today.ToString("MM/dd/yyyy");
                case ParameterType.Boolean:
                    return "true";
                default:
                    return "";
            }
        }
    }
}
