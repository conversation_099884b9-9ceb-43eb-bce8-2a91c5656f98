# PrintPreviewForm Implementation Guide

## Overview
The PrintPreviewForm is a centralized print preview system for the ProManage application that replaces individual form backstage views. It provides a reusable, professional document viewer that can display reports from any module in the system, ensuring consistent user experience and reducing code duplication.

## Architecture Overview

### Centralized Print Preview Benefits
- **Single Responsibility**: One form handles all print preview functionality across the entire application
- **Consistent UI**: Standardized toolbar and viewer experience for all reports
- **Code Reusability**: Eliminates need for individual backstage views in each form
- **Maintainability**: Centralized error handling and feature updates
- **Professional Features**: Full DevExpress document viewer capabilities

### PrintPreviewForm Features
- **Document Viewer**: Uses DevExpress DocumentViewer (`documentViewer1`) for professional report display
- **Toolbar Controls**: Print, Print Preview (refresh), Find, Zoom, and Export functionality
- **Reusable Design**: Can be called from any form with any XtraReport
- **Error Handling**: Comprehensive error handling and user feedback
- **Resource Management**: Proper cleanup of resources when form closes
- **Modal Display**: Shows as modal dialog to maintain focus on report viewing

### Key Components

#### Core Properties
- `currentReport`: Private XtraReport field that stores the currently loaded report
- `documentViewer1`: DevExpress DocumentViewer control for report display
- `barManager1`: DevExpress BarManager for toolbar functionality

#### Toolbar Buttons
- `btnPrint`: Direct print functionality
- `btnPrintPreview`: Refresh/regenerate current report preview
- `btnFind`: Search functionality (uses Ctrl+F shortcut)
- `btnZoom`: Zoom controls with track bar
- `barButtonItem3`: Export functionality (PDF, Excel, Word)

## Core Methods

### LoadReport(XtraReport report, string reportTitle)
The primary method for loading and displaying reports:

```csharp
public void LoadReport(XtraReport report, string reportTitle = "Print Preview")
{
    try
    {
        if (report == null)
            throw new ArgumentNullException(nameof(report), "Report cannot be null");

        // Store reference to current report
        currentReport = report;

        // Update form title
        this.Text = reportTitle;

        // Generate the document
        report.CreateDocument();

        // Display in document viewer
        documentViewer1.DocumentSource = report;
    }
    catch (Exception ex)
    {
        MessageBox.Show($"Error loading report: {ex.Message}", "Report Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

**Key Features:**
- Null validation for report parameter
- Dynamic form title setting
- Document generation and viewer binding
- Comprehensive error handling with user feedback

### Toolbar Event Handlers

#### Print Functionality
```csharp
private void BtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    if (currentReport == null)
    {
        MessageBox.Show("No report is currently loaded.", "No Report",
            MessageBoxButtons.OK, MessageBoxIcon.Information);
        return;
    }
    currentReport.Print();
}
```

#### Export Functionality
```csharp
private void BtnExport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    using (var saveDialog = new SaveFileDialog())
    {
        saveDialog.Filter = "PDF Files (*.pdf)|*.pdf|Excel Files (*.xlsx)|*.xlsx|Word Files (*.docx)|*.docx|All Files (*.*)|*.*";
        saveDialog.DefaultExt = "pdf";

        if (saveDialog.ShowDialog() == DialogResult.OK)
        {
            string extension = System.IO.Path.GetExtension(saveDialog.FileName).ToLower();
            switch (extension)
            {
                case ".pdf": currentReport.ExportToPdf(saveDialog.FileName); break;
                case ".xlsx": currentReport.ExportToXlsx(saveDialog.FileName); break;
                case ".docx": currentReport.ExportToDocx(saveDialog.FileName); break;
                default: currentReport.ExportToPdf(saveDialog.FileName); break;
            }
        }
    }
}
```

## Implementation Steps

### Step 1: Add Print Preview Button to Form Ribbon

#### 1.1 Designer Setup
Add a `BarButtonItem` to your form's ribbon control:

```csharp
// In the form designer or designer.cs file
private DevExpress.XtraBars.BarButtonItem BarButtonPrintPreview;

// Initialize the button
this.BarButtonPrintPreview = new DevExpress.XtraBars.BarButtonItem();
this.BarButtonPrintPreview.Caption = "Print Preview";
this.BarButtonPrintPreview.Id = [unique_id];
this.BarButtonPrintPreview.Name = "BarButtonPrintPreview";
this.BarButtonPrintPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BarButtonPrintPreview_ItemClick);
```

#### 1.2 Add Button to Ribbon Group
```csharp
// Add to appropriate ribbon page group
this.ribbonPageGroup.ItemLinks.Add(this.BarButtonPrintPreview);
```

### Step 2: Implement Event Handler Setup

#### 2.1 Setup Button Events Method
Create a method to wire up button events (typically called in form constructor):

```csharp
private void SetupButtonEvents()
{
    try
    {
        // Print Preview button event
        BarButtonPrintPreview.ItemClick += (s, e) => ShowPrintPreview();

        Debug.WriteLine("Button events setup completed");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error setting up button events: {ex.Message}");
        throw;
    }
}
```

#### 2.2 Designer Fallback Handler
Add a fallback handler for designer-generated events:

```csharp
private void BarButtonPrintPreview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    // Call our main print preview method
    ShowPrintPreview();
}
```

### Step 3: Implement ShowPrintPreview Method

#### 3.1 Basic Implementation Pattern
```csharp
private void ShowPrintPreview()
{
    try
    {
        Debug.WriteLine("=== ShowPrintPreview: Starting ===");

        // Step 1: Validate that data is loaded
        if (currentDataObject == null)
        {
            MessageBox.Show("No data is currently loaded. Please select a record first.",
                "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        Debug.WriteLine($"Generating print preview for: {currentDataObject.Identifier}");

        // Step 2: Generate the report using your report service
        var report = YourReportService.CreateReport(this);

        if (report == null)
        {
            MessageBox.Show("Failed to create report. Please check the data and try again.",
                "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        Debug.WriteLine("Report generated successfully, creating print preview form...");

        // Step 3: Create and show the print preview form
        var printPreviewForm = new ProManage.Forms.UserControlsForms.PrintPreviewForm();

        // Step 4: Load the report with a descriptive title
        string reportTitle = $"Your Report Type - {currentDataObject.Identifier}";
        printPreviewForm.LoadReport(report, reportTitle);

        // Step 5: Show the form as a modal dialog
        printPreviewForm.ShowDialog(this);

        Debug.WriteLine("=== ShowPrintPreview: Completed ===");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in ShowPrintPreview: {ex.Message}");
        MessageBox.Show($"Error showing print preview: {ex.Message}", "Print Preview Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

### Step 4: Create Report Service

#### 4.1 Report Service Structure
Create a dedicated service class for your report generation:

```csharp
namespace ProManage.Modules.Reports
{
    public static class YourReportService
    {
        public static YourXtraReport CreateReport(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== CreateReport: Starting ===");

                // Extract data from form
                var headerData = ExtractHeaderDataFromForm(form);
                var detailData = ExtractDetailDataFromForm(form);

                // Create and populate the report
                var report = new YourXtraReport();
                report.PopulateReportData(headerData, detailData);

                Debug.WriteLine("=== CreateReport: Completed ===");
                return report;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CreateReport: {ex.Message}");
                throw;
            }
        }

        private static YourHeaderModel ExtractHeaderDataFromForm(dynamic form)
        {
            // Extract header data from form controls
            return new YourHeaderModel
            {
                Field1 = form.txtField1.Text?.Trim(),
                Field2 = form.txtField2.Text?.Trim(),
                Date = form.dpDate.DateTime,
                // ... other fields
            };
        }

        private static List<YourDetailModel> ExtractDetailDataFromForm(dynamic form)
        {
            // Extract detail data from grid or other controls
            var detailData = new List<YourDetailModel>();
            var gridDataTable = form.GridDataTable;

            if (gridDataTable != null)
            {
                foreach (System.Data.DataRow row in gridDataTable.Rows)
                {
                    if (!IsEmptyRow(row))
                    {
                        detailData.Add(new YourDetailModel
                        {
                            Field1 = GetStringValue(row, "Column1"),
                            Field2 = GetDecimalValue(row, "Column2"),
                            // ... other fields
                        });
                    }
                }
            }

            return detailData;
        }
    }
}
```

## Concrete Implementation Example: EstimateForm

### EstimateForm Integration
The EstimateForm demonstrates the complete implementation pattern:

#### Button Setup in EstimateForm
```csharp
private void SetupButtonEvents()
{
    try
    {
        // Print Preview button event
        BarButtonPrintPreview.ItemClick += (s, e) => ShowPrintPreview();

        Debug.WriteLine("Button events setup completed");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error setting up button events: {ex.Message}");
        throw;
    }
}
```

#### ShowPrintPreview Implementation
```csharp
private void ShowPrintPreview()
{
    try
    {
        Debug.WriteLine("=== ShowPrintPreview: Starting ===");

        // Check if we have a current estimate
        if (currentEstimate == null)
        {
            MessageBox.Show("No estimate is currently loaded. Please select an estimate first.",
                "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        Debug.WriteLine($"Generating print preview for estimate: {currentEstimate.EstimateNo}");

        // Generate the report using the existing report service
        var report = ProManage.Modules.Reports.EstimateReportService.CreateEstimateReport(this);

        if (report == null)
        {
            MessageBox.Show("Failed to create report. Please check the data and try again.",
                "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        Debug.WriteLine("Report generated successfully, creating print preview form...");

        // Create and show the print preview form
        var printPreviewForm = new ProManage.Forms.UserControlsForms.PrintPreviewForm();

        // Load the report with a descriptive title
        string reportTitle = $"Estimate Print Preview - {currentEstimate.EstimateNo}";
        printPreviewForm.LoadReport(report, reportTitle);

        // Show the form as a modal dialog
        printPreviewForm.ShowDialog(this);

        Debug.WriteLine("=== ShowPrintPreview: Completed ===");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in ShowPrintPreview: {ex.Message}");
        MessageBox.Show($"Error showing print preview: {ex.Message}", "Print Preview Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

#### EstimateReportService Implementation
```csharp
public static class EstimateReportService
{
    public static ProManage.Reports.EstimatePrint CreateEstimateReport(dynamic form)
    {
        try
        {
            Debug.WriteLine("=== CreateEstimateReport: Starting ===");

            // Extract current estimate data from form
            var headerData = ExtractHeaderDataFromForm(form);
            var detailData = ExtractDetailDataFromForm(form);

            // Create and populate the report
            var report = new ProManage.Reports.EstimatePrint();
            report.PopulateReportData(headerData, detailData);

            Debug.WriteLine($"=== CreateEstimateReport: Completed ===");
            return report;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error in CreateEstimateReport: {ex.Message}");
            throw;
        }
    }

    private static EstimateFormHeaderModel ExtractHeaderDataFromForm(dynamic form)
    {
        return new EstimateFormHeaderModel
        {
            EstimateNo = form.txtEstimate.Text?.Trim(),
            CustomerName = form.txtCustomer.Text?.Trim(),
            VehicleModel = form.txtVehicle.Text?.Trim(),
            VIN = form.txtVIN.Text?.Trim(),
            Brand = form.cbBrand.Text?.Trim(),
            Location = form.cbLocation.Text?.Trim(),
            SalesmanName = form.txtSalesman.Text?.Trim(),
            DocDate = form.dpDocDate.DateTime,
            Remarks = form.txtDocRemarks.Text?.Trim()
        };
    }

    private static List<EstimateFormDetailModel> ExtractDetailDataFromForm(dynamic form)
    {
        var detailData = new List<EstimateFormDetailModel>();
        var gridDataTable = form.GridDataTable;

        if (gridDataTable != null && gridDataTable.Rows.Count > 0)
        {
            int serialNumber = 1;
            foreach (System.Data.DataRow row in gridDataTable.Rows)
            {
                if (!IsEmptyRow(row))
                {
                    detailData.Add(new EstimateFormDetailModel
                    {
                        SerialNo = serialNumber++,
                        PartNo = GetStringValue(row, "PartNumber"),
                        Description = GetStringValue(row, "Description"),
                        Qty = GetIntValue(row, "Quantity"),
                        OEPrice = GetDecimalValue(row, "OEPrice"),
                        AFMPrice = GetDecimalValue(row, "AFMPrice"),
                        Remarks = GetStringValue(row, "Remarks"),
                        ApproveStatus = GetBooleanValue(row, "Status")
                    });
                }
            }
        }

        return detailData;
    }
}
```

## Database Integration and Field Mapping

### Report Data Population
The EstimatePrint report demonstrates proper field mapping:

```csharp
public void PopulateReportData(EstimateFormHeaderModel headerData, List<EstimateFormDetailModel> detailData)
{
    try
    {
        // Populate header information
        PopulateHeaderData(headerData);

        // Populate detail data
        PopulateDetailData(detailData);

        // Calculate and populate totals
        PopulateTotals(detailData);
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in PopulateReportData: {ex.Message}");
        throw;
    }
}

private void PopulateHeaderData(EstimateFormHeaderModel headerData)
{
    // Customer information
    customerName.Text = headerData.CustomerName ?? "N/A";
    customerAddress.Text = $"{headerData.VehicleModel} - VIN: {headerData.VIN}";

    // Estimate information
    invoiceNumber.Text = headerData.EstimateNo ?? "N/A";
    invoiceDate.Text = headerData.DocDate?.ToString("dd MMM yyyy") ?? DateTime.Now.ToString("dd MMM yyyy");

    // Valid till (72 hours after estimate date)
    var validTillDate = (headerData.DocDate ?? DateTime.Now).AddHours(72);
    total2.Text = validTillDate.ToString("dd MMM yyyy");

    // Salesman and location information
    vendorName.Text = headerData.SalesmanName ?? "N/A";
    vendorPhone.Text = headerData.Location ?? "N/A";

    // Remarks
    thankYouLabel.Text = !string.IsNullOrWhiteSpace(headerData.Remarks)
        ? headerData.Remarks
        : "Thank you for your business!";
}

private void PopulateDetailData(List<EstimateFormDetailModel> detailData)
{
    if (detailData == null || !detailData.Any())
    {
        // Create empty DataTable to clear the detail section
        var emptyTable = CreateDetailDataTable();
        this.DataSource = emptyTable;
        return;
    }

    // Create DataTable for detail binding
    var detailTable = CreateDetailDataTable();

    foreach (var detail in detailData)
    {
        var row = detailTable.NewRow();
        row["ProductName"] = detail.PartNo ?? "";
        row["ProductDescription"] = detail.Description ?? "";
        row["Quantity"] = detail.Qty;
        row["UnitPrice"] = detail.AFMPrice; // Using AFM price as primary
        row["LineTotal"] = detail.Qty * detail.AFMPrice;
        detailTable.Rows.Add(row);
    }

    // Bind the data to the report
    this.DataSource = detailTable;
}
```

## Error Handling Patterns

### Comprehensive Error Handling
The PrintPreviewForm implements multiple layers of error handling:

#### 1. Form-Level Error Handling
```csharp
private void ShowPrintPreview()
{
    try
    {
        // Main logic here
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in ShowPrintPreview: {ex.Message}");
        MessageBox.Show($"Error showing print preview: {ex.Message}", "Print Preview Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

#### 2. Report Service Error Handling
```csharp
public static YourXtraReport CreateReport(dynamic form)
{
    try
    {
        // Report generation logic
        return report;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in CreateReport: {ex.Message}");
        throw; // Re-throw to be handled by calling method
    }
}
```

#### 3. PrintPreviewForm Internal Error Handling
```csharp
public void LoadReport(XtraReport report, string reportTitle = "Print Preview")
{
    try
    {
        if (report == null)
            throw new ArgumentNullException(nameof(report), "Report cannot be null");

        // Load report logic
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in LoadReport: {ex.Message}");
        MessageBox.Show($"Error loading report: {ex.Message}", "Report Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

#### 4. Resource Cleanup
```csharp
protected override void OnFormClosing(FormClosingEventArgs e)
{
    try
    {
        // Clean up report reference
        currentReport = null;

        // Clear document viewer
        if (documentViewer1 != null)
        {
            documentViewer1.DocumentSource = null;
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error cleaning up PrintPreviewForm: {ex.Message}");
    }

    base.OnFormClosing(e);
}
```

## Reusability Guidelines

### Applying to New Forms

#### Step-by-Step Checklist
1. **Add Print Preview Button**: Add `BarButtonPrintPreview` to form's ribbon
2. **Setup Event Handler**: Wire button to `ShowPrintPreview()` method
3. **Implement ShowPrintPreview**: Follow the standard pattern with validation
4. **Create Report Service**: Implement dedicated service for report generation
5. **Create Data Models**: Define header and detail models for your data
6. **Create XtraReport**: Design the report layout using DevExpress Report Designer
7. **Implement PopulateReportData**: Map form data to report fields

#### Naming Conventions
- **Report Service**: `{FormName}ReportService` (e.g., `InvoiceReportService`)
- **Report Class**: `{FormName}Print` (e.g., `InvoicePrint`)
- **Data Models**: `{FormName}HeaderModel`, `{FormName}DetailModel`
- **Service Location**: `Modules\Reports\{FormName}\`

#### Standard File Structure
```
Modules\Reports\{FormName}\
├── {FormName}ReportService.cs          // Report generation service
├── {FormName}-PrintLayout.cs           // XtraReport class
├── {FormName}-PrintLayout.Designer.cs  // Report designer file
└── {FormName}-PrintLayout.resx         // Report resources
```

### Forms That Can Implement This Pattern

#### Current Forms
- **EstimateForm**: ✅ Already implemented
- **ParametersForm**: Can implement for parameter lists/reports
- **MainFrame**: Not applicable (navigation form)

#### Future Forms
- **InvoiceForm**: Invoice printing and preview
- **PurchaseOrderForm**: Purchase order reports
- **InventoryForm**: Inventory reports and listings
- **CustomerForm**: Customer information reports
- **VendorForm**: Vendor reports and statements

#### Implementation Priority
1. **High Priority**: Forms with transactional data (Invoice, Purchase Order)
2. **Medium Priority**: Forms with master data (Customer, Vendor, Inventory)
3. **Low Priority**: Forms with configuration data (Parameters, Settings)

### Code Templates

#### Basic ShowPrintPreview Template
```csharp
private void ShowPrintPreview()
{
    try
    {
        Debug.WriteLine("=== ShowPrintPreview: Starting ===");

        // Validate current data
        if (current{DataObject} == null)
        {
            MessageBox.Show("No {data type} is currently loaded. Please select a {data type} first.",
                "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        Debug.WriteLine($"Generating print preview for {data type}: {current{DataObject}.Identifier}");

        // Generate report
        var report = ProManage.Modules.Reports.{FormName}ReportService.Create{FormName}Report(this);

        if (report == null)
        {
            MessageBox.Show("Failed to create report. Please check the data and try again.",
                "Report Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        Debug.WriteLine("Report generated successfully, creating print preview form...");

        // Show print preview
        var printPreviewForm = new ProManage.Forms.UserControlsForms.PrintPreviewForm();
        string reportTitle = $"{Report Type} Print Preview - {current{DataObject}.Identifier}";
        printPreviewForm.LoadReport(report, reportTitle);
        printPreviewForm.ShowDialog(this);

        Debug.WriteLine("=== ShowPrintPreview: Completed ===");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error in ShowPrintPreview: {ex.Message}");
        MessageBox.Show($"Error showing print preview: {ex.Message}", "Print Preview Error",
            MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

## Technical Notes

### Dependencies
- **DevExpress.XtraReports.UI**: Core reporting functionality
- **DevExpress.XtraPrinting.Preview**: Document viewer control
- **DevExpress.XtraBars**: Ribbon and toolbar controls
- **System.Diagnostics**: Debug logging
- **System.Windows.Forms**: Standard Windows Forms controls

### Performance Considerations
- **On-Demand Generation**: Reports are generated only when requested
- **Memory Management**: Document viewer handles large reports efficiently
- **Resource Cleanup**: Proper disposal prevents memory leaks
- **Modal Display**: Prevents multiple preview windows and resource conflicts

### Best Practices
1. **Always validate data** before generating reports
2. **Use descriptive report titles** that include record identifiers
3. **Implement comprehensive error handling** at all levels
4. **Follow consistent naming conventions** for maintainability
5. **Use Debug.WriteLine** for troubleshooting and monitoring
6. **Clean up resources** in form closing events
7. **Test with various data scenarios** including empty/null data

### Troubleshooting Common Issues

#### Report Not Displaying
- Check if report is null before calling LoadReport
- Verify report.CreateDocument() completes successfully
- Ensure documentViewer1.DocumentSource is set correctly

#### Export Functionality Not Working
- Verify file path permissions for export location
- Check if report data is properly bound
- Ensure export format is supported

#### Memory Issues
- Implement proper resource cleanup in OnFormClosing
- Avoid keeping references to large reports
- Use using statements for disposable resources

### Future Enhancements

#### Planned Features
1. **Multi-Report Support**: Ability to load multiple reports in tabs
2. **Print Settings**: Advanced print configuration options
3. **Report History**: Recently viewed reports functionality
4. **Custom Export Options**: Additional export formats and settings
5. **Report Templates**: Standardized report layouts across forms

#### Integration Opportunities
- **Email Integration**: Direct email sending from preview
- **Cloud Storage**: Export directly to cloud storage services
- **Batch Processing**: Generate multiple reports simultaneously
- **Report Scheduling**: Automated report generation and distribution
