using System;
using System.Diagnostics;

namespace ProManage
{
    /// <summary>
    /// Resolves type references and ensures proper namespace resolution
    /// This class helps resolve namespace issues that can occur when converting from VB.NET to C#
    /// </summary>
    public static class TypeResolver
    {
        /// <summary>
        /// Initializes type references to ensure they are properly loaded by the compiler
        /// Call this method at application startup to prevent type resolution issues
        /// </summary>
        public static void InitializeTypeReferences()
        {
            Debug.WriteLine("Initializing type references...");

            // Reference model types
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.EstimateFormHeaderModel));
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.EstimateFormDetailModel));
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.LoginFormUserModel));

            // Reference module types
            EnsureTypeLoaded(typeof(ProManage.Modules.Validation.EstimateFormValidation));
            EnsureTypeLoaded(typeof(ProManage.Modules.UI.EstimateFormUI));
            EnsureTypeLoaded(typeof(ProManage.Modules.Helpers.EstimateFormGridHelpers));
            EnsureTypeLoaded(typeof(ProManage.Modules.EventHandlers.EstimateFormEventHandlers));
            EnsureTypeLoaded(typeof(ProManage.Modules.Data.EstimateFormDataAccess));

            Debug.WriteLine("Type references initialized successfully");
        }

        /// <summary>
        /// Ensures a type is loaded by the compiler
        /// </summary>
        /// <param name="type">The type to ensure is loaded</param>
        private static void EnsureTypeLoaded(Type type)
        {
            Debug.WriteLine($"Ensuring type is loaded: {type.FullName}");
        }
    }
}
